<?php
/**
 * PXE API Integration Script
 * 
 * This script integrates the web interface with actual network infrastructure
 * for DHCP reservations and PXE server configuration.
 */

require_once 'mysql.php';
require_once 'auth_functions.php';
require_once 'api_admin_subnets.php'; // For ACL management functions

/**
 * Ensure that the PXE reinstall tables exist. Creates them if they are missing.
 */
if (!function_exists('ensurePXESchema')) {
    function ensurePXESchema(PDO $pdo)
    {
        static $executed = false;
        if ($executed) {
            return;
        }
        $executed = true;

        // pxe_reinstall_sessions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS `pxe_reinstall_sessions` (
            `id` int NOT NULL AUTO_INCREMENT,
            `server_id` int NOT NULL,
            `server_type` enum('dedicated','blade') NOT NULL DEFAULT 'dedicated',
            `server_label` varchar(255) NOT NULL,
            `mac_address` varchar(17) NOT NULL,
            `ip_address` varchar(45) NOT NULL,
            `hostname` varchar(255) NOT NULL,
            `os_template` varchar(100) NOT NULL,
            `status` enum('pending','active','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
            `dhcp_configured` tinyint(1) DEFAULT 0,
            `files_created` tinyint(1) DEFAULT 0,
            `acl_removed` tinyint(1) DEFAULT 0,
            `acl_reapplied` tinyint(1) DEFAULT 0,
            `acl_removal_error` text DEFAULT NULL,
            `acl_reapply_error` text DEFAULT NULL,
            `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `completed_at` timestamp NULL DEFAULT NULL,
            `error_message` text,
            `network_config` json,
            PRIMARY KEY (`id`),
            KEY `idx_server` (`server_id`,`server_type`),
            KEY `idx_status` (`status`),
            KEY `idx_started` (`started_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci;");

        // Add ACL tracking columns if they don't exist (for existing installations)
        try {
            $pdo->exec("ALTER TABLE `pxe_reinstall_sessions`
                ADD COLUMN IF NOT EXISTS `acl_removed` tinyint(1) DEFAULT 0,
                ADD COLUMN IF NOT EXISTS `acl_reapplied` tinyint(1) DEFAULT 0,
                ADD COLUMN IF NOT EXISTS `acl_removal_error` text DEFAULT NULL,
                ADD COLUMN IF NOT EXISTS `acl_reapply_error` text DEFAULT NULL");
        } catch (Exception $e) {
            // Ignore errors if columns already exist
        }

        // pxe_dhcp_entries table
        $pdo->exec("CREATE TABLE IF NOT EXISTS `pxe_dhcp_entries` (
            `id` int NOT NULL AUTO_INCREMENT,
            `session_id` int NOT NULL,
            `mac_address` varchar(17) NOT NULL,
            `ip_address` varchar(45) NOT NULL,
            `hostname` varchar(255) NOT NULL,
            `server_label` varchar(255) NOT NULL,
            `dhcp_entry_added` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `dhcp_entry_removed` timestamp NULL DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            KEY `fk_session` (`session_id`),
            KEY `idx_mac` (`mac_address`),
            KEY `idx_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci;");
    }
}

class PXENetworkManager {
    private $pdo;
    private $config;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->config = [
            'scripts_path' => '/usr/local/bin',
            'autoinstall_base_url' => 'http://' . (isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'auto.x-zoneit.ro'),
            'autoinstall_root' => '/var/www/html/New/autoinstall',
            'dhcp_config_file' => '/etc/dhcp/dhcpd.conf',
            'log_file' => 'auto.logs'
        ];
    }
    
    /**
     * Get network configuration for a server's IP address from database
     */
    private function getNetworkConfigFromDatabase($server_ip) {
        try {
                    // Handle server IP calculation from subnet CIDR
        $ip_address = $server_ip;
        $subnet_cidr = null;
        
        if (strpos($server_ip, '/') !== false) {
            // If we have a CIDR (like *************/28), this is the subnet, not the server IP
            list($network_ip, $prefix) = explode('/', $server_ip);
            $subnet_cidr = $server_ip;
            
            // Calculate server IP from subnet: network + 2 (gateway is network + 1)
            $network_long = ip2long($network_ip);
            $server_ip_long = $network_long + 2; // Skip network (.80) and gateway (.81), use .82
            $ip_address = long2ip($server_ip_long);
            
            $this->logOperation("Calculated server IP from subnet CIDR", [
                'original_subnet_cidr' => $server_ip,
                'network_ip' => $network_ip,
                'calculated_server_ip' => $ip_address,
                'prefix' => $prefix
            ]);
        }
            
            $this->logOperation("Getting network configuration for IP: $ip_address", [
                'original_server_ip' => $server_ip,
                'extracted_ip' => $ip_address
            ]);
            
            // Find the subnet that contains this IP address
            $stmt = $this->pdo->prepare("
                SELECT 
                    s.*,
                    INET_NTOA(INET_ATON(SUBSTRING_INDEX(s.subnet, '/', 1)) + 1) AS gateway,
                    POWER(2, (32 - s.subnet_size)) - 1 AS subnet_mask_long,
                    CASE s.subnet_size
                        WHEN 24 THEN '*************'
                        WHEN 25 THEN '***************'
                        WHEN 26 THEN '***************'
                        WHEN 27 THEN '***************'
                        WHEN 28 THEN '***************'
                        WHEN 29 THEN '***************'
                        WHEN 30 THEN '***************'
                        WHEN 31 THEN '***************'
                        WHEN 32 THEN '***************'
                        WHEN 23 THEN '*************'
                        WHEN 22 THEN '*************'
                        WHEN 21 THEN '*************'
                        WHEN 20 THEN '*************'
                        WHEN 19 THEN '*************'
                        WHEN 18 THEN '*************'
                        WHEN 17 THEN '*************'
                        WHEN 16 THEN '***********'
                        ELSE '*************'
                    END AS subnet_mask
                FROM subnets s 
                WHERE 
                    INET_ATON(:ip_address) >= INET_ATON(SUBSTRING_INDEX(s.subnet, '/', 1)) 
                    AND INET_ATON(:ip_address) <= INET_ATON(SUBSTRING_INDEX(s.subnet, '/', 1)) + POW(2, (32 - s.subnet_size)) - 1
                ORDER BY s.subnet_size DESC
                LIMIT 1
            ");
            $stmt->execute(['ip_address' => $ip_address]);
            $subnet = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->logOperation("Subnet lookup result", [
                'ip_searched' => $ip_address,
                'subnet_found' => $subnet ? $subnet['subnet'] : 'none',
                'query_executed' => true
            ]);
            
            if (!$subnet && $subnet_cidr) {
                // Fallback: try to find by exact match with the original CIDR
                $this->logOperation("No subnet found for calculated IP, trying exact CIDR match", [
                    'calculated_ip' => $ip_address,
                    'original_cidr' => $subnet_cidr
                ]);
                $stmt = $this->pdo->prepare("
                    SELECT 
                        s.*,
                        INET_NTOA(INET_ATON(SUBSTRING_INDEX(s.subnet, '/', 1)) + 1) AS gateway,
                        CASE s.subnet_size
                            WHEN 24 THEN '*************'
                            WHEN 25 THEN '***************'
                            WHEN 26 THEN '***************'
                            WHEN 27 THEN '***************'
                            WHEN 28 THEN '***************'
                            WHEN 29 THEN '***************'
                            WHEN 30 THEN '***************'
                            WHEN 31 THEN '***************'
                            WHEN 32 THEN '***************'
                            WHEN 23 THEN '*************'
                            WHEN 22 THEN '*************'
                            WHEN 21 THEN '*************'
                            WHEN 20 THEN '*************'
                            WHEN 19 THEN '*************'
                            WHEN 18 THEN '*************'
                            WHEN 17 THEN '*************'
                            WHEN 16 THEN '***********'
                            ELSE '*************'
                        END AS subnet_mask
                    FROM subnets s 
                    WHERE s.subnet = :subnet_cidr
                    LIMIT 1
                ");
                $stmt->execute(['subnet_cidr' => $subnet_cidr]);
                $subnet = $stmt->fetch(PDO::FETCH_ASSOC);
            }
            
            // Get DNS servers from general_settings
            $dns_primary = '8.8.8.8'; // default
            $dns_secondary = '8.8.4.4'; // default
            
            try {
                $dns_stmt = $this->pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'dns_primary' LIMIT 1");
                $dns_stmt->execute();
                if ($dns_result = $dns_stmt->fetch(PDO::FETCH_ASSOC)) {
                    $dns_primary = $dns_result['setting_value'];
                }
                
                $dns_stmt = $this->pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'dns_secondary' LIMIT 1");
                $dns_stmt->execute();
                if ($dns_result = $dns_stmt->fetch(PDO::FETCH_ASSOC)) {
                    $dns_secondary = $dns_result['setting_value'];
                }
            } catch (Exception $e) {
                $this->logOperation("Failed to fetch DNS settings, using defaults", ['error' => $e->getMessage()]);
            }
            
            if ($subnet) {
                // Always use the original server IP, not the network address
                $network_config = [
                    'ip_address' => $ip_address, // This is the server's actual IP (************)
                    'subnet_mask' => $subnet['subnet_mask'],
                    'gateway' => $subnet['gateway'],
                    'dns_primary' => $dns_primary,
                    'dns_secondary' => $dns_secondary,
                    'subnet_cidr' => $subnet['subnet'],
                    'subnet_id' => $subnet['id']
                ];
                
                $this->logOperation("Network configuration found from database", [
                    'original_server_ip' => $ip_address,
                    'found_subnet' => $subnet['subnet'],
                    'calculated_gateway' => $subnet['gateway'],
                    'final_config' => $network_config
                ]);
                return $network_config;
            } else {
                // Fallback to calculated values based on IP
                $this->logOperation("No subnet found in database, calculating network config", ['ip' => $ip_address]);
                
                // Try to determine subnet from IP pattern
                $ip_parts = explode('.', $ip_address);
                if (count($ip_parts) === 4) {
                    // Common /30 subnet pattern for server IPs
                    $network_base_last_octet = floor($ip_parts[3] / 4) * 4;
                    $network_base = $ip_parts[0] . '.' . $ip_parts[1] . '.' . $ip_parts[2] . '.' . $network_base_last_octet;
                    $gateway_ip = $ip_parts[0] . '.' . $ip_parts[1] . '.' . $ip_parts[2] . '.' . ($network_base_last_octet + 1);
                    
                    return [
                        'ip_address' => $ip_address,
                        'subnet_mask' => '***************', // /30
                        'gateway' => $gateway_ip,
                        'dns_primary' => $dns_primary,
                        'dns_secondary' => $dns_secondary,
                        'subnet_cidr' => $network_base . '/30',
                        'subnet_id' => null
                    ];
                }
                
                throw new Exception("Unable to determine network configuration for IP: $ip_address");
            }
            
        } catch (Exception $e) {
            $this->logOperation("Error getting network configuration: " . $e->getMessage(), ['ip' => $server_ip]);
            throw $e;
        }
    }
    
    /**
     * Complete PXE setup for a server - creates all necessary files
     */
    public function setupCompleteReinstall($server_id, $server_data, $network_config, $os_template, $custom_password = null, $session_id = null) {
        try {
            $this->logOperation("Setting up complete PXE reinstall for server $server_id", [
                'hostname' => $network_config['hostname'],
                'os_template' => $os_template
            ]);
            
            // Validate inputs
            $this->validateNetworkConfig($network_config);
            $mac_clean = $this->formatMacAddress($server_data['mac']);
            
            if (!$mac_clean) {
                throw new Exception('Invalid MAC address format');
            }
            
            $hostname = $network_config['hostname'];
            $servername = $this->extractServerName($hostname);
            
            // Always generate a new password for each reinstall unless explicitly provided
            if ($custom_password === null) {
                $custom_password = $this->generateRandomPassword();
                $this->logOperation("Generated new password for server reinstall", [
                    'server_id' => $server_id,
                    'password_length' => strlen($custom_password)
                ]);
            }
            
            // Create all necessary files. Pass $session_id forward so that the generated
            // user-data can callback the API to trigger automatic cleanup when the install
            // finishes on first boot.
            $this->createDHCPEntry($server_data, $network_config, $mac_clean);
            $this->createAutoinstallFiles($servername, $server_data, $network_config, $os_template, $custom_password, $session_id, $server_id);

            // Configure switch interface with IP helper-address for PXE reinstall
            $switch_configured = false;
            try {
                $this->logOperation("Configuring switch interface for PXE reinstall", [
                    'server_id' => $server_id,
                    'ip_address' => $network_config['ip_address']
                ]);

                // Include the subnet configuration functions
                require_once 'api_admin_subnets.php';

                // Determine server type from server_data or assume dedicated
                $server_type = $server_data['server_type'] ?? 'dedicated';

                // Get server information including switch details (same as subnet assignment)
                $server_table = $server_type === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
                $server_info_stmt = $this->pdo->prepare("
                    SELECT s.*, sw.switch_ip, sw.root_password, sw.label as switch_label
                    FROM $server_table s
                    LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
                    WHERE s.id = :server_id
                ");
                $server_info_stmt->bindValue(':server_id', $server_id);
                $server_info_stmt->execute();
                $server_info = $server_info_stmt->fetch(PDO::FETCH_ASSOC);

                if ($server_info && !empty($server_info['switch_ip']) && !empty($server_info['root_password']) && !empty($server_info['port1'])) {
                    // Resolve port name (use port_name from inventory_switch_ports)
                    $server_port = $server_info['port1'];
                    if (is_numeric($server_port)) {
                        $get_port_stmt = $this->pdo->prepare("SELECT port_name, port_number FROM inventory_switch_ports WHERE id = :port_id AND switch_id = :switch_id");
                        $get_port_stmt->bindValue(':port_id', $server_port);
                        $get_port_stmt->bindValue(':switch_id', $server_info['switch_id']);
                        $get_port_stmt->execute();

                        if ($get_port_stmt->rowCount() > 0) {
                            $port_info = $get_port_stmt->fetch(PDO::FETCH_ASSOC);
                            // Use port_name (e.g., "Ethernet35") instead of port_number (e.g., "35")
                            $server_port = !empty($port_info['port_name']) ? $port_info['port_name'] : $port_info['port_number'];
                            $this->logOperation("PXE Reinstall: Resolved port ID to port name", [
                                'server_id' => $server_id,
                                'port_id' => $server_info['port1'],
                                'port_name' => $server_port
                            ]);
                        } else {
                            $this->logOperation("PXE Reinstall: Could not resolve port ID", [
                                'server_id' => $server_id,
                                'port_id' => $server_info['port1'],
                                'switch_id' => $server_info['switch_id']
                            ]);
                        }
                    }

                    // Get all subnets assigned to this server (like in subnet assignment)
                    $all_subnets = [];

                    // Add main IP if exists
                    if (!empty($server_info['main_ip'])) {
                        $all_subnets[] = $server_info['main_ip'];
                    }

                    // Add additional IPs if exist
                    if (!empty($server_info['additional_ips'])) {
                        $additional_ips = array_filter(array_map('trim', explode(',', $server_info['additional_ips'])));
                        $all_subnets = array_merge($all_subnets, $additional_ips);
                    }

                    if (!empty($all_subnets)) {
                        // Calculate gateway IP from first subnet (like in subnet assignment)
                        $primary_subnet = $all_subnets[0];
                        $gateway_ip = $primary_subnet;
                        if (strpos($primary_subnet, '/') !== false) {
                            list($net_ip, $prefix) = explode('/', $primary_subnet);
                            $gateway_ip = long2ip(ip2long($net_ip) + 1);
                        }

                        $this->logOperation("PXE Reinstall: Configuring switch with subnets", [
                            'server_id' => $server_id,
                            'switch_ip' => $server_info['switch_ip'],
                            'port' => $server_port,
                            'subnets' => $all_subnets,
                            'gateway_ip' => $gateway_ip
                        ]);

                        // Queue switch configuration operation (same as subnet assignment)
                        $operation_id = queueSwitchOperation('configure', $server_info['switch_ip'], $server_port, [
                            'password' => $server_info['root_password'],
                            'ip_address' => $gateway_ip,
                            'subnet_info' => $all_subnets,
                            'server_label' => $server_info['label'] ?? null
                        ], $this->pdo);

                        if ($operation_id) {
                            $this->logOperation("PXE Reinstall: Switch configuration queued", [
                                'server_id' => $server_id,
                                'operation_id' => $operation_id
                            ]);

                            // Process the queue immediately (like in subnet assignment)
                            processSwitchQueue($this->pdo);

                            $switch_configured = true;
                            $this->logOperation("Switch interface configured successfully for PXE reinstall", [
                                'server_id' => $server_id
                            ]);
                        } else {
                            $this->logOperation("Failed to queue switch configuration for PXE reinstall", [
                                'server_id' => $server_id
                            ]);
                        }
                    } else {
                        $this->logOperation("No subnets found for server, skipping switch configuration", [
                            'server_id' => $server_id
                        ]);
                    }
                } else {
                    $this->logOperation("Server missing switch info, skipping switch configuration", [
                        'server_id' => $server_id,
                        'switch_ip' => $server_info['switch_ip'] ?? 'null',
                        'root_password' => empty($server_info['root_password']) ? 'null' : 'set',
                        'port1' => $server_info['port1'] ?? 'null'
                    ]);
                }
            } catch (Exception $switch_error) {
                $this->logOperation("Switch configuration error during PXE reinstall", [
                    'server_id' => $server_id,
                    'error' => $switch_error->getMessage()
                ]);
            }

            $this->logOperation("Complete PXE setup completed successfully", [
                'server_id' => $server_id,
                'servername' => $servername,
                'files_created' => [
                    'dhcp_entry' => true,
                    'boot_ipxe' => true,
                    'meta_data' => true,
                    'user_data' => true
                ],
                'switch_configured' => $switch_configured
            ]);
            
            return [
                'success' => true,
                'message' => 'Complete PXE setup completed successfully',
                'details' => [
                    'servername' => $servername,
                    'hostname' => $hostname,
                    'ip_address' => $network_config['ip_address'],
                    'mac_address' => $mac_clean,
                    'files' => [
                        'dhcp_config' => $this->config['dhcp_config_file'],
                        'boot_ipxe' => "/var/www/html/New/autoinstall/servers/$servername/boot.ipxe",
                        'meta_data' => "/var/www/html/New/autoinstall/servers/$servername/meta-data",
                        'user_data' => "/var/www/html/New/autoinstall/servers/$servername/user-data"
                    ]
                ]
            ];
            
        } catch (Exception $e) {
            $this->logOperation("Complete PXE setup failed: " . $e->getMessage(), ['server_id' => $server_id]);
            throw $e;
        }
    }
    
    /**
     * Create or update DHCP configuration entry
     */

     private function cidrToNetmask($cidr) {
        $cidr = intval($cidr);
        $netmask = str_split(str_pad(str_pad('', $cidr, '1'), 32, '0'), 8);
        foreach ($netmask as &$element) {
            $element = bindec($element);
        }
        return implode('.', $netmask);
    }
    
 /**
 * Create or update DHCP configuration entry with dedicated subnet per server
 */
private function createDHCPEntry($server_data, $network_config, $mac_clean) {
    $hostname = $network_config['hostname'];
    $server_ip = $network_config['ip_address']; // This should be the actual server IP
    $servername = $this->extractServerName($hostname);
    
    // Debug logging to see what network config we're using
    $this->logOperation("Creating DHCP entry with network config", [
        'hostname' => $hostname,
        'server_ip' => $server_ip,
        'servername' => $servername,
        'full_network_config' => $network_config
    ]);
    
    // Check if DHCP config file exists and is writable
    if (!file_exists($this->config['dhcp_config_file'])) {
        $this->logOperation("DHCP config file not found, skipping DHCP update", [
            'file' => $this->config['dhcp_config_file']
        ]);
        return; // Don't throw exception, just skip DHCP update
    }
    
    if (!is_writable($this->config['dhcp_config_file'])) {
        $this->logOperation("DHCP config file not writable, skipping DHCP update", [
            'file' => $this->config['dhcp_config_file']
        ]);
        return; // Don't throw exception, just skip DHCP update
    }
    
    try {
        $dhcp_content = file_get_contents($this->config['dhcp_config_file']);
        if ($dhcp_content === false) {
            throw new Exception("Failed to read DHCP configuration file");
        }
        
        // Remove existing entry for this server if it exists
        $dhcp_content = $this->removeDHCPEntry($dhcp_content, $servername);
        
        // Determine the subnet information for this server
        $subnet_info = $this->calculateSubnetInfo($server_ip, $network_config);
        
        // Generate complete subnet block with host entry inside (like in dhcpd.conf example)
        $new_subnet_block = $this->generateCompleteSubnetBlock($servername, $hostname, $mac_clean, $server_ip, $network_config, $subnet_info);
        
        // Insert the complete subnet block into the shared-network pxe
        $dhcp_content = $this->insertSubnetIntoSharedNetwork($dhcp_content, $new_subnet_block);
        
        // Write updated DHCP config
        if (file_put_contents($this->config['dhcp_config_file'], $dhcp_content) === false) {
            throw new Exception("Failed to write DHCP configuration");
        }
        
        // Restart DHCP service automatically
        exec('sudo systemctl restart isc-dhcp-server 2>&1', $output, $return_code);
        if ($return_code !== 0) {
            $this->logOperation("DHCP service restart failed, but configuration was updated", $output);
        } else {
            $this->logOperation("DHCP service restarted successfully", []);
        }
        
        $this->logOperation("DHCP entry created/updated successfully", [
            'servername' => $servername,
            'hostname' => $hostname,
            'ip' => $server_ip,
            'mac' => $mac_clean,
            'subnet' => $subnet_info['subnet_cidr']
        ]);
        
    } catch (Exception $e) {
        $this->logOperation("DHCP configuration failed: " . $e->getMessage(), [
            'servername' => $servername,
            'error' => $e->getMessage()
        ]);
        // Don't re-throw the exception, just log it
    }
}

/**
 * Calculate subnet information for a given IP address
 */
private function calculateSubnetInfo($ip_address, $network_config) {
    // Get subnet CIDR from network config if available
    if (isset($network_config['subnet_cidr']) && $network_config['subnet_cidr']) {
        list($subnet_network, $prefix) = explode('/', $network_config['subnet_cidr']);
    } else {
        // Calculate subnet based on IP and netmask
        $subnet_mask = $network_config['subnet_mask'];
        $subnet_network = long2ip(ip2long($ip_address) & ip2long($subnet_mask));
        $prefix = $this->subnetMaskToCIDR($subnet_mask);
    }
    
    // Calculate broadcast address
    $subnet_long = ip2long($subnet_network);
    $mask_long = ip2long($network_config['subnet_mask']);
    $broadcast_long = $subnet_long | (~$mask_long & 0xFFFFFFFF);
    $broadcast = long2ip($broadcast_long);
    
    return [
        'subnet_network' => $subnet_network,
        'subnet_mask' => $network_config['subnet_mask'],
        'subnet_cidr' => "$subnet_network/$prefix",
        'gateway' => $network_config['gateway'],
        'broadcast' => $broadcast
    ];
}

/**
 * Insert a host entry into the appropriate subnet block within the shared-network
 */
private function insertHostIntoSubnet($dhcp_content, $new_host_entry, $subnet_info) {
    // First, check if the shared-network pxe block exists
    if (!preg_match('/shared-network\s+pxe\s*\{/s', $dhcp_content)) {
        // Create the shared-network block
        $dhcp_content .= "\n#SERVERS CONFIGURATION FOR AUTOINSTALL\n";
        $dhcp_content .= "shared-network pxe {\n}\n";
    }
    
    // Check if a subnet block already exists for this subnet
    $subnet_pattern = '/subnet\s+' . preg_quote($subnet_info['subnet_network'], '/') . '\s+netmask\s+' . preg_quote($subnet_info['subnet_mask'], '/') . '\s*\{[^}]*\}/s';
    
    if (preg_match($subnet_pattern, $dhcp_content, $matches)) {
        // Subnet block exists, insert the host into it
        $subnet_block = $matches[0];
        
        // Find the last brace of the subnet block and insert before it
        $last_brace_pos = strrpos($subnet_block, '}');
        $updated_subnet = substr($subnet_block, 0, $last_brace_pos) . 
                         "\n" . $new_host_entry . "\n" . 
                         substr($subnet_block, $last_brace_pos);
        
        $dhcp_content = str_replace($subnet_block, $updated_subnet, $dhcp_content);
    } else {
        // Subnet block doesn't exist, create it with the host inside
        $new_subnet_block = $this->generateSubnetBlock($subnet_info, $new_host_entry);
        
        // Insert the new subnet block inside the shared-network pxe block
        $pattern = '/(shared-network\s+pxe\s*\{.*?)(\s*\}\s*)$/s';
        if (preg_match($pattern, $dhcp_content)) {
            $dhcp_content = preg_replace(
                $pattern,
                '$1' . "\n" . $new_subnet_block . "\n$2",
                $dhcp_content
            );
        }
    }
    
    return $dhcp_content;
}

/**
 * Generate a complete subnet block with the host entry inside (like dhcpd.conf example)
 */
private function generateCompleteSubnetBlock($servername, $hostname, $mac_address, $server_ip, $network_config, $subnet_info) {
    // Always use the server_ip parameter passed to this function - this is the actual server IP
    $this->logOperation("Generating subnet block", [
        'servername' => $servername,
        'server_ip' => $server_ip,
        'subnet_network' => $subnet_info['subnet_network'],
        'gateway' => $subnet_info['gateway']
    ]);
    
    // Add comment with server name
    $subnet_block = "    # PXE subnet ($servername)\n";
    $subnet_block .= "    subnet {$subnet_info['subnet_network']} netmask {$subnet_info['subnet_mask']} {\n";
    $subnet_block .= "        option subnet-mask {$subnet_info['subnet_mask']};\n";
    $subnet_block .= "        option routers {$subnet_info['gateway']};\n";
    $subnet_block .= "        option broadcast-address {$subnet_info['broadcast']};\n";
    
    // Calculate range for DHCP allocation (avoid network, gateway, and server IP)
    $range = $this->calculateSafeRange($subnet_info, $server_ip);
    if ($range) {
        $subnet_block .= "        range {$range['start']} {$range['end']};  # Safe IP range\n";
    }
    
    $subnet_block .= "        \n";
    
    // Add host entry - use the server IP parameter directly
    $subnet_block .= "        host $servername {\n";
    $subnet_block .= "            hardware ethernet $mac_address;\n";
    $subnet_block .= "            fixed-address $server_ip;\n";  // Use server IP parameter directly
    $subnet_block .= "            option host-name \"$hostname\";\n";
    $subnet_block .= "            \n";
    $subnet_block .= "            # Boot logic based on client architecture and user-class\n";
    $subnet_block .= "            if exists user-class and option user-class = \"iPXE\" {\n";
    $subnet_block .= "                filename \"http://auto.x-zoneit.ro/servers/$servername/boot.ipxe\";\n";
    $subnet_block .= "            } elsif option client-architecture = 00:07 {  # UEFI x64\n";
    $subnet_block .= "                filename \"snponly.efi\";\n";
    $subnet_block .= "            } elsif option client-architecture = 00:00 {  # Legacy BIOS\n";
    $subnet_block .= "                filename \"undionly.kpxe\";\n";
    $subnet_block .= "            } else {\n";
    $subnet_block .= "                filename \"undionly.kpxe\";\n";
    $subnet_block .= "            }\n";
    $subnet_block .= "        }\n";
    $subnet_block .= "    }";
    
    return $subnet_block;
}

/**
 * Calculate safe IP range for DHCP allocation within subnet
 */
private function calculateSafeRange($subnet_info, $server_ip) {
    $network_long = ip2long($subnet_info['subnet_network']);
    $mask_long = ip2long($subnet_info['subnet_mask']);
    $broadcast_long = ip2long($subnet_info['broadcast']);
    $server_long = ip2long($server_ip);
    $gateway_long = ip2long($subnet_info['gateway']);
    
    // For small subnets like /30, we might not have a range
    $subnet_size = 32 - log((~$mask_long & 0xFFFFFFFF) + 1, 2);
    
    if ($subnet_size >= 28) { // /28 or smaller (16 IPs or less)
        // For small subnets, calculate a careful range
        $reserved_ips = [$network_long, $gateway_long, $server_long, $broadcast_long];
        
        // Find available IPs in the subnet
        $available_ips = [];
        for ($ip_long = $network_long + 1; $ip_long < $broadcast_long; $ip_long++) {
            if (!in_array($ip_long, $reserved_ips)) {
                $available_ips[] = $ip_long;
            }
        }
        
        // If we have at least 2 available IPs, create a range
        if (count($available_ips) >= 2) {
            sort($available_ips);
            return [
                'start' => long2ip($available_ips[0]),
                'end' => long2ip($available_ips[count($available_ips) - 1])
            ];
        }
    } else {
        // For larger subnets, create a reasonable range avoiding reserved IPs
        $start_long = $network_long + 4; // Skip network, gateway, and a few others
        $end_long = $broadcast_long - 4; // Skip broadcast and a few others
        
        // Ensure we don't include the server IP in the range
        if ($server_long >= $start_long && $server_long <= $end_long) {
            // Adjust range to avoid server IP
            if ($server_long - $start_long > $end_long - $server_long) {
                // More space before server IP
                $end_long = $server_long - 1;
            } else {
                // More space after server IP
                $start_long = $server_long + 1;
            }
        }
        
        // Ensure we don't include the gateway IP in the range
        if ($gateway_long >= $start_long && $gateway_long <= $end_long) {
            // Adjust range to avoid gateway IP
            if ($gateway_long - $start_long > $end_long - $gateway_long) {
                // More space before gateway IP
                $end_long = $gateway_long - 1;
            } else {
                // More space after gateway IP
                $start_long = $gateway_long + 1;
            }
        }
        
        if ($start_long <= $end_long) {
            return [
                'start' => long2ip($start_long),
                'end' => long2ip($end_long)
            ];
        }
    }
    
    return null; // No safe range available
}

/**
 * Insert a complete subnet block into the shared-network pxe
 */
private function insertSubnetIntoSharedNetwork($dhcp_content, $subnet_block) {
    // First, check if the shared-network pxe block exists
    if (!preg_match('/shared-network\s+pxe\s*\{/s', $dhcp_content)) {
        // Create the shared-network block
        $dhcp_content .= "shared-network pxe {\n}\n";
    }
    
    // Insert the subnet block inside the shared-network pxe block
    $pattern = '/(shared-network\s+pxe\s*\{.*?)(\s*\}\s*)$/s';
    if (preg_match($pattern, $dhcp_content)) {
        $dhcp_content = preg_replace(
            $pattern,
            '$1' . "\n" . $subnet_block . "\n$2",
            $dhcp_content
        );
    }
    
    return $dhcp_content;
}

/**
 * Generate a complete subnet block with the host entry inside
 */
private function generateSubnetBlock($subnet_info, $host_entry) {
    $subnet_block = "  subnet {$subnet_info['subnet_network']} netmask {$subnet_info['subnet_mask']} {\n";
    $subnet_block .= "    option subnet-mask {$subnet_info['subnet_mask']};\n";
    $subnet_block .= "    option routers {$subnet_info['gateway']};\n";
    $subnet_block .= "    option broadcast-address {$subnet_info['broadcast']};\n";
    $subnet_block .= "    \n";
    $subnet_block .= $host_entry . "\n";
    $subnet_block .= "  }";
    
    return $subnet_block;
}

/**
 * Generate DHCP entry for a server (host block only, no subnet wrapper)
 */
private function generateDHCPEntry($servername, $hostname, $mac_address, $ip_address, $network_config) {
    $mac_upper = strtoupper(str_replace(':', '-', $mac_address));
    $mac_formatted = strtoupper($mac_address);
    
    $dhcp_entry = "    host h-$mac_upper {\n";
    $dhcp_entry .= "      hardware ethernet $mac_address;\n";
    $dhcp_entry .= "      fixed-address $ip_address;\n";
    $dhcp_entry .= "      option host-name \"$hostname\";\n";
    $dhcp_entry .= "      \n";
    $dhcp_entry .= "      # Boot logic based on client architecture and user-class\n";
    $dhcp_entry .= "      if exists user-class and option user-class = \"iPXE\" {\n";
    $dhcp_entry .= "        # Client already has iPXE, send the final boot script\n";
    $dhcp_entry .= "        filename \"http://auto.x-zoneit.ro/servers/$servername/boot.ipxe\";\n";
    $dhcp_entry .= "      } elsif option client-architecture = 00:07 {\n";
    $dhcp_entry .= "        # UEFI x64 - send UEFI iPXE binary\n";
    $dhcp_entry .= "        filename \"snponly.efi\";\n";
    $dhcp_entry .= "      } elsif option client-architecture = 00:00 {\n";
    $dhcp_entry .= "        # Legacy BIOS - send legacy iPXE binary\n";
    $dhcp_entry .= "        filename \"undionly.kpxe\";\n";
    $dhcp_entry .= "      } else {\n";
    $dhcp_entry .= "        # Default fallback for unknown architectures\n";
    $dhcp_entry .= "        filename \"undionly.kpxe\";\n";
    $dhcp_entry .= "      }\n";
    $dhcp_entry .= "    }";
    
    return $dhcp_entry;
}
    
/**
 * Remove existing DHCP entry for a server (entire subnet block)
 * This version removes the complete subnet block for the server
 */
private function removeDHCPEntry($dhcp_content, $servername) {
    $this->logOperation("Starting DHCP entry removal", ['servername' => $servername]);
    
    // Step 1: Remove the entire subnet block for this server
    $result = $this->removeServerSubnetBlock($dhcp_content, $servername);
    $dhcp_content = $result['content'];
    $subnet_removed = $result['removed'];
    
    if ($subnet_removed) {
        // Step 2: Clean up any empty shared-network blocks
        $dhcp_content = $this->removeEmptySharedNetworks($dhcp_content);
        
        // Step 3: Clean up excessive whitespace
        $dhcp_content = preg_replace('/\n{3,}/', "\n\n", $dhcp_content);
        $dhcp_content = trim($dhcp_content) . "\n";
    }
    
    $this->logOperation("DHCP entry removal completed", [
        'servername' => $servername,
        'subnet_removed' => $subnet_removed
    ]);
    
    return $dhcp_content;
}

/**
 * Remove the entire subnet block for a specific server
 */
private function removeServerSubnetBlock($content, $servername) {
    $lines = explode("\n", $content);
    $new_lines = [];
    $removed = false;
    
    $i = 0;
    while ($i < count($lines)) {
        $line = $lines[$i];
        
        // Check if this is the start of a subnet block
        if (preg_match('/^\s*subnet\s+[\d.]+\s+netmask\s+[\d.]+/i', $line)) {
            // Found a subnet block, extract it to check if it contains our server
            $subnet_block = $this->extractBlock($lines, $i, 'subnet');
            
            // Check if this subnet contains our server (look for filename path or host name)
            if (strpos($subnet_block['content'], "/servers/$servername/") !== false ||
                strpos($subnet_block['content'], "host $servername") !== false ||
                strpos($subnet_block['content'], "# PXE subnet ($servername)") !== false) {
                
                // This is the subnet for our server, remove it completely
                $i = $subnet_block['end_index'] + 1;
                $removed = true;
                $this->logOperation("Found and removed subnet block for server", [
                    'servername' => $servername,
                    'lines_removed' => $subnet_block['end_index'] - $i + 1
                ]);
            } else {
                // Not our server's subnet, keep it
                for ($j = $i; $j <= $subnet_block['end_index']; $j++) {
                    $new_lines[] = $lines[$j];
                }
                $i = $subnet_block['end_index'] + 1;
            }
        } else {
            // Not a subnet line, keep it
            $new_lines[] = $line;
            $i++;
        }
    }
    
    return [
        'content' => implode("\n", $new_lines),
        'removed' => $removed
    ];
}

/**
 * Remove a specific host block from DHCP configuration
 */
private function removeHostBlock($content, $servername) {
    $lines = explode("\n", $content);
    $new_lines = [];
    $removed = false;
    
    $i = 0;
    while ($i < count($lines)) {
        $line = $lines[$i];
        
        // Check if this is the start of a host block
        if (preg_match('/^\s*host\s+h-/i', $line)) {
            // Found a host block, check if it's the one we want to remove
            $host_block = $this->extractBlock($lines, $i, 'host');
            
            if (strpos($host_block['content'], "/servers/$servername/") !== false) {
                // This is the host we want to remove, skip it
                $i = $host_block['end_index'] + 1;
                $removed = true;
                $this->logOperation("Found and removed host block", [
                    'servername' => $servername,
                    'lines_removed' => $host_block['end_index'] - $i + 1
                ]);
            } else {
                // Not our host, keep it
                $new_lines[] = $line;
                $i++;
            }
        } else {
            // Not a host line, keep it
            $new_lines[] = $line;
            $i++;
        }
    }
    
    return [
        'content' => implode("\n", $new_lines),
        'removed' => $removed
    ];
}

/**
 * Extract a complete block (host, subnet, shared-network) starting from a given line
 */
private function extractBlock($lines, $start_index, $block_type) {
    $content = '';
    $brace_count = 0;
    $found_opening_brace = false;
    $end_index = $start_index;
    
    for ($i = $start_index; $i < count($lines); $i++) {
        $line = $lines[$i];
        $content .= $line . "\n";
        
        // Count braces
        $open_braces = substr_count($line, '{');
        $close_braces = substr_count($line, '}');
        
        if ($open_braces > 0) {
            $found_opening_brace = true;
        }
        
        $brace_count += $open_braces - $close_braces;
        
        // If we've found the opening brace and brace count is back to 0, we're done
        if ($found_opening_brace && $brace_count <= 0) {
            $end_index = $i;
            break;
        }
    }
    
    return [
        'content' => $content,
        'start_index' => $start_index,
        'end_index' => $end_index
    ];
}

/**
 * Remove empty subnet blocks (subnets that contain no hosts)
 */
private function removeEmptySubnets($content) {
    $lines = explode("\n", $content);
    $new_lines = [];
    
    $i = 0;
    while ($i < count($lines)) {
        $line = $lines[$i];
        
        // Check if this is the start of a subnet block
        if (preg_match('/^\s*subnet\s+[\d.]+\s+netmask\s+[\d.]+/i', $line)) {
            // Found a subnet block
            $subnet_block = $this->extractBlock($lines, $i, 'subnet');
            
            // Check if this subnet contains any host definitions
            if (preg_match('/\bhost\s+h-/i', $subnet_block['content'])) {
                // Subnet has hosts, keep it
                for ($j = $i; $j <= $subnet_block['end_index']; $j++) {
                    $new_lines[] = $lines[$j];
                }
            } else {
                $this->logOperation("Removed empty subnet block", [
                    'subnet_line' => trim($line)
                ]);
            }
            
            $i = $subnet_block['end_index'] + 1;
        } else {
            // Not a subnet line, keep it
            $new_lines[] = $line;
            $i++;
        }
    }
    
    return implode("\n", $new_lines);
}

/**
 * Remove empty shared-network blocks
 */
private function removeEmptySharedNetworks($content) {
    $lines = explode("\n", $content);
    $new_lines = [];
    
    $i = 0;
    while ($i < count($lines)) {
        $line = $lines[$i];
        
        // Check if this is the start of a shared-network block
        if (preg_match('/^\s*shared-network\s+/i', $line)) {
            // Found a shared-network block
            $network_block = $this->extractBlock($lines, $i, 'shared-network');
            
            // Check if this shared-network contains any subnet definitions
            if (preg_match('/\bsubnet\s+[\d.]+\s+netmask\s+[\d.]+/i', $network_block['content'])) {
                // Network has subnets, keep it
                for ($j = $i; $j <= $network_block['end_index']; $j++) {
                    $new_lines[] = $lines[$j];
                }
            } else {
                $this->logOperation("Removed empty shared-network block", [
                    'network_line' => trim($line)
                ]);
            }
            
            $i = $network_block['end_index'] + 1;
        } else {
            // Not a shared-network line, keep it
            $new_lines[] = $line;
            $i++;
        }
    }
    
    return implode("\n", $new_lines);
}
         /**
     * Create autoinstall files for the server
     */
     private function createAutoinstallFiles($servername, $server_data, $network_config, $os_template, $custom_password = null, $session_id = null, $server_id = null) {
         $server_dir = $this->config['autoinstall_root'] . "/servers/$servername";
         
         // Check if autoinstall root directory exists
         if (!is_dir($this->config['autoinstall_root'])) {
             $this->logOperation("Autoinstall root directory not found, skipping file creation", [
                 'dir' => $this->config['autoinstall_root']
             ]);
             return; // Don't throw exception, just skip file creation
         }
         
         // Check if servers directory exists and is writable
         $servers_dir = $this->config['autoinstall_root'] . "/servers";
         if (!is_dir($servers_dir)) {
             if (!mkdir($servers_dir, 0755, true)) {
                 $this->logOperation("Failed to create servers directory, skipping file creation", [
                     'dir' => $servers_dir
                 ]);
                 return;
             }
         }
         
         // Create server directory if it doesn't exist
         if (!is_dir($server_dir)) {
             if (!mkdir($server_dir, 0755, true)) {
                 $this->logOperation("Failed to create server directory, skipping file creation", [
                     'dir' => $server_dir
                 ]);
                 return;
             }
         }
         
         try {
             // Generate and save boot.ipxe
             $boot_ipxe_content = $this->generateBootIPXE($servername, $os_template);
             if (file_put_contents("$server_dir/boot.ipxe", $boot_ipxe_content) === false) {
                 throw new Exception("Failed to create boot.ipxe file");
             }
             
            // Check if this uses preseed instead of cloud-init (Ubuntu 18.04, Debian)
            if ((stripos($os_template, 'ubuntu-18.04') !== false || stripos($os_template, 'ubuntu 18.04') !== false) || 
                (stripos($os_template, 'debian') !== false)) {
                 // For Ubuntu 18.04 and Debian, create preseed.cfg instead of meta-data/user-data
                 $network_config_with_mac = $network_config;
                 $network_config_with_mac['mac'] = $server_data['mac'] ?? '';
                 
                 // Choose appropriate preseed generator
                if (stripos($os_template, 'ubuntu-18.04') !== false || stripos($os_template, 'ubuntu 18.04') !== false) {
                    // Prepare cleanup info for Ubuntu 18.04
                    $cleanup_info = [
                        'server_id'  => $server_id,
                        'session_id' => $session_id,
                        'hostname'   => $network_config['hostname'] ?? '',
                        'servername' => $servername
                    ];
                    $preseed_content = $this->generateUbuntu1804Preseed($network_config_with_mac, $custom_password, $cleanup_info, $server_data);
                } else {
                    // Prepare cleanup info for Debian
                    $cleanup_info = [
                        'server_id'  => $server_id,
                        'session_id' => $session_id,
                        'hostname'   => $network_config['hostname'] ?? '',
                        'servername' => $servername
                    ];
                    $preseed_content = $this->generatePreseed($network_config_with_mac, $custom_password, $cleanup_info, $server_data);
                }
                 
                 if (file_put_contents("$server_dir/preseed.cfg", $preseed_content) === false) {
                     throw new Exception("Failed to create preseed.cfg file");
                 }
                 @chmod("$server_dir/preseed.cfg", 0644);
            
            // Handle Proxmox VE separately (uses TOML answer file)
            } elseif (stripos($os_template, 'proxmox') !== false) {
                $network_config_with_mac = $network_config;
                $network_config_with_mac['mac'] = $server_data['mac'] ?? '';
                
                // Prepare cleanup info for Proxmox
                $cleanup_info = [
                    'server_id'  => $server_id,
                    'session_id' => $session_id,
                    'hostname'   => $network_config['hostname'] ?? '',
                    'servername' => $servername
                ];
                
                $answer_content = $this->generateProxmoxAnswerFile($network_config_with_mac, $custom_password, $cleanup_info, $server_data);
                if (file_put_contents("$server_dir/proxmox-answer.toml", $answer_content) === false) {
                    throw new Exception("Failed to create proxmox-answer.toml file");
                }
                @chmod("$server_dir/proxmox-answer.toml", 0644);
             } else {
             // Generate and save meta-data
             $meta_data_content = $this->generateMetaData($network_config['hostname']);
             if (file_put_contents("$server_dir/meta-data", $meta_data_content) === false) {
                 throw new Exception("Failed to create meta-data file");
             }
             
             // Generate and save user-data
             // Inject MAC address for netplan match clause
             $network_config_with_mac = $network_config;
             $network_config_with_mac['mac'] = $server_data['mac'] ?? '';

             // Prepare cleanup info to be embedded in cloud-init
             $cleanup_info = [
                 'server_id'  => $server_id,
                 'session_id' => $session_id,
                 'hostname'   => $network_config['hostname'] ?? '',
                 'servername' => $servername
             ];

             $user_data_content = $this->generateUserData($network_config_with_mac, $custom_password, $cleanup_info, $server_data);
             if (file_put_contents("$server_dir/user-data", $user_data_content) === false) {
                 throw new Exception("Failed to create user-data file");
                 }
                 @chmod("$server_dir/user-data", 0644);
             }
             
             // ---------------------------------------------------------
             // Additional unattended install files for CentOS/AlmaLinux
             // ---------------------------------------------------------
             if (stripos($os_template, 'centos') === 0 || stripos($os_template, 'almalinux') === 0) {
                 $ks_content = $this->generateKickstart($network_config_with_mac, $custom_password, $cleanup_info, $server_data);
                 file_put_contents("$server_dir/ks.cfg", $ks_content);
                 @chmod("$server_dir/ks.cfg", 0644);
             }
             
             // ---------------------------------------------------------
             // Additional unattended install files for VMware ESXi
             // ---------------------------------------------------------
             if (stripos($os_template, 'esxi') !== false) {
                 $ks_content = $this->generateESXiKickstart($network_config_with_mac, $custom_password, $cleanup_info, $server_data);
                 file_put_contents("$server_dir/ks.cfg", $ks_content);
                 @chmod("$server_dir/ks.cfg", 0644);
             }
             // Note: Debian preseed.cfg is now handled in the main logic above
             
             // Set proper permissions (ignore errors if chmod fails)
             @chmod("$server_dir/boot.ipxe", 0644);
             
            // Determine created files based on OS template
            $created_files = ['boot.ipxe'];
            if ((stripos($os_template, 'ubuntu-18.04') !== false || stripos($os_template, 'ubuntu 18.04') !== false) || 
                (stripos($os_template, 'debian') !== false)) {
                $created_files[] = 'preseed.cfg';
            } elseif (stripos($os_template, 'proxmox') !== false) {
                $created_files[] = 'proxmox-answer.toml';
            } else {
             @chmod("$server_dir/meta-data", 0644);
                 $created_files[] = 'meta-data';
                 $created_files[] = 'user-data';
             }
             
             if (stripos($os_template, 'centos') === 0 || stripos($os_template, 'almalinux') === 0) { $created_files[] = 'ks.cfg'; }
             if (stripos($os_template, 'esxi') !== false) { $created_files[] = 'ks.cfg'; }
             // Note: preseed.cfg for Debian is already tracked above
             
             $this->logOperation("Autoinstall files created successfully", [
                 'servername' => $servername,
                 'server_dir' => $server_dir,
                 'files'      => $created_files
             ]);
             
         } catch (Exception $e) {
             $this->logOperation("Autoinstall file creation failed: " . $e->getMessage(), [
                 'servername' => $servername,
                 'server_dir' => $server_dir,
                 'error' => $e->getMessage()
             ]);
             // Don't re-throw the exception, just log it
         }
     }
    
    /**
     * Generate boot.ipxe content
     */
    private function generateBootIPXE($servername, $os_template) {
        // Check if this is Ubuntu 18.04 which needs special preseed handling
        if (stripos($os_template, 'ubuntu-18.04') !== false || stripos($os_template, 'ubuntu 18.04') !== false) {
            return $this->generateUbuntu1804BootIPXE($servername);
        }
        
        // Check if this is Debian which needs special preseed handling
        if (stripos($os_template, 'debian') !== false) {
            return $this->generateDebianBootIPXE($servername, $os_template);
        }
        
        // Check if this is CentOS/RHEL/AlmaLinux which needs special handling
        if (stripos($os_template, 'centos') !== false || 
            stripos($os_template, 'rhel') !== false || 
            stripos($os_template, 'almalinux') !== false) {
            return $this->generateCentOSBootIPXE($servername, $os_template);
        }
        
        // Check if this is ESXi which needs special handling
        if (stripos($os_template, 'esxi') !== false || stripos($os_template, 'vmware') !== false) {
            return $this->generateESXiBootIPXE($servername, $os_template);
        }
        
        // Check if this is Proxmox which needs special handling
        if (stripos($os_template, 'proxmox') !== false) {
            return $this->generateProxmoxBootIPXE($servername, $os_template);
        }

        // Retrieve detailed OS configuration (path, kernel, initrd, ISO, boot params)
        $cfg = $this->getOSConfig($os_template);

        $os_path      = $cfg['path'];
        $kernel_file  = $cfg['kernel'];
        $initrd_file  = $cfg['initrd'];
        $iso_file     = $cfg['iso'];

        // Replace placeholder with actual server directory name
        $boot_params  = str_replace('%SERVER%', $servername, $cfg['boot_params']);

        $content  = "#!ipxe\n";
        $content .= "kernel http://auto.x-zoneit.ro/os/{$os_path}/{$kernel_file} initrd={$initrd_file} root=/dev/ram0 ramdisk_size=1500000 ip=dhcp ";

        // Append ISO parameter when defined (Ubuntu & others)
        if (!empty($iso_file)) {
            $content .= "url=http://auto.x-zoneit.ro/os/{$os_path}/{$iso_file} ";
        }

        $content .= "$boot_params\n";
        $content .= "initrd http://auto.x-zoneit.ro/os/{$os_path}/{$initrd_file}\n";
        $content .= "boot\n";

        return $content;
    }
    
    /**
     * Generate special boot.ipxe content for Ubuntu 18.04 using preseed
     */
    private function generateUbuntu1804BootIPXE($servername) {
        $content  = "#!ipxe\n";
        $content .= "set base-url http://auto.x-zoneit.ro/os/ubuntu-18.04/\n";
        $content .= "kernel \${base-url}/linux initrd=initrd.gz preseed/url=http://auto.x-zoneit.ro/servers/$servername/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp\n";
        $content .= "initrd \${base-url}/initrd.gz\n";
        $content .= "boot\n";

        return $content;
    }
    
    /**
     * Generate special boot.ipxe content for Debian using preseed
     */
    private function generateDebianBootIPXE($servername, $os_template) {
        // Retrieve OS configuration to get the correct path
        $cfg = $this->getOSConfig($os_template);
        $os_path = $cfg['path'];
        
        $content  = "#!ipxe\n";
        $content .= "set base-url http://auto.x-zoneit.ro/os/$os_path/\n";
        $content .= "kernel \${base-url}/linux initrd=initrd.gz preseed/url=http://auto.x-zoneit.ro/servers/$servername/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp\n";
        $content .= "initrd \${base-url}/initrd.gz\n";
        $content .= "boot\n";

        return $content;
    }
    
    /**
     * Generate special boot.ipxe content for CentOS/RHEL/AlmaLinux
     */
    private function generateCentOSBootIPXE($servername, $os_template) {
        // Determine OS type and version from template name
        $os_type = "centos";
        $version = "9";
        
        if (stripos($os_template, 'almalinux') !== false) {
            $os_type = "almalinux";
            if (stripos($os_template, 'almalinux-8') !== false || stripos($os_template, 'almalinux 8') !== false) {
                $version = "8";
            } elseif (stripos($os_template, 'almalinux-9') !== false || stripos($os_template, 'almalinux 9') !== false) {
                $version = "9";
            } elseif (stripos($os_template, 'almalinux-10') !== false || stripos($os_template, 'almalinux 10') !== false) {
                $version = "10";
            }
        } elseif (stripos($os_template, 'centos') !== false) {
            $os_type = "centos";
            if (stripos($os_template, 'centos-9') !== false || stripos($os_template, 'centos 9') !== false) {
                $version = "9";
            } elseif (stripos($os_template, 'centos-10') !== false || stripos($os_template, 'centos 10') !== false) {
                $version = "10";
            } elseif (stripos($os_template, 'centos-8') !== false || stripos($os_template, 'centos 8') !== false) {
                $version = "8";
            } elseif (stripos($os_template, 'centos-7') !== false || stripos($os_template, 'centos 7') !== false) {
                $version = "7";
            }
        }
        
        $repo = "http://auto.x-zoneit.ro/os/{$os_type}-{$version}/";
        $ks_url = "http://auto.x-zoneit.ro/servers/{$servername}/ks.cfg";
        
        $content  = "#!ipxe\n";
        $content .= "imgfree\n";
        $content .= "set ipparam BOOTIF=\${net0/mac} ip=dhcp\n";
        $content .= "set repo {$repo}\n";
        $content .= "set ks_url {$ks_url}\n";
        // Include all necessary boot parameters:
        // - inst.repo: Repository URL
        // - inst.ks: Kickstart URL
        // - ipparam: Network parameters using MAC address of the NIC
        $content .= "kernel \${repo}/images/pxeboot/vmlinuz inst.repo=\${repo} inst.ks=\${ks_url} \${ipparam} initrd=initrd.img\n";
        $content .= "initrd \${repo}/images/pxeboot/initrd.img\n";
        $content .= "boot\n";
        
        return $content;
    }
    
    /**
     * Generate special boot.ipxe content for VMware ESXi
     */
    private function generateESXiBootIPXE($servername, $os_template) {
        // Determine ESXi version from template name
        $version = "8.0";
        if (stripos($os_template, 'esxi-7') !== false || stripos($os_template, 'esxi 7') !== false) {
            $version = "7.0";
        } elseif (stripos($os_template, 'esxi-8') !== false || stripos($os_template, 'esxi 8') !== false) {
            $version = "8.0";
        } elseif (stripos($os_template, 'esxi-9') !== false || stripos($os_template, 'esxi 9') !== false) {
            $version = "9.0";
        }
        
        $base_url = "http://auto.x-zoneit.ro/os/esxi-{$version}/";
        $ks_url = "http://auto.x-zoneit.ro/servers/{$servername}/ks.cfg";
        
        $content  = "#!ipxe\n";
        $content .= "imgfree\n";
        $content .= "set base-url {$base_url}\n";
        $content .= "set ks-url {$ks_url}\n";
        $content .= "\n";
        
        // Use different boot methods based on ESXi version
        if ($version === "7.0") {
            $content .= "# ESXi 7.0 boot configuration (better compatibility with older hardware)\n";
            $content .= "kernel \${base-url}/mboot.c32 -c \${base-url}/boot.cfg ks=\${ks-url}\n";
        } else {
            $content .= "# ESXi {$version} boot configuration with CPU compatibility overrides\n";
            $content .= "kernel \${base-url}/efi/boot/bootx64.efi -c \${base-url}/boot.cfg ks=\${ks-url} allowLegacyCPU=true cpuUniformityHardCheckPanic=FALSE ignoreHeadless=TRUE systemMediaSize=min\n";
        }
        
        $content .= "boot\n";
        
        return $content;
    }
    
    /**
     * Generate special boot.ipxe content for Proxmox VE
     * Uses kernel/initrd from extracted ISO for network installation
     */
    private function generateProxmoxBootIPXE($servername, $os_template) {
        // Determine Proxmox version from template name
        $version = "8.2";
        if (stripos($os_template, 'proxmox-7') !== false || stripos($os_template, 'proxmox 7') !== false) {
            $version = "7.4";
        } elseif (stripos($os_template, 'proxmox-8') !== false || stripos($os_template, 'proxmox 8') !== false) {
            $version = "8.2";
        }
        
        $base_url = "http://auto.x-zoneit.ro/os/proxmox-{$version}/";
        
        $content  = "#!ipxe\n";
        $content .= "imgfree\n";
        $content .= "set base-url {$base_url}\n";
        $content .= "set timeout 30000\n";
        $content .= "\n";
        
        // Check Proxmox version for different handling
        if (version_compare($version, '8.0', '>=')) {
            // Proxmox VE 8.x with automated installation support
            $content .= "# Proxmox VE {$version} with automated installation\n";
            $content .= "menu Proxmox VE Installation Menu\n";
            $content .= "item automated Install Proxmox VE {$version} (Automated)\n";
            $content .= "item manual Install Proxmox VE {$version} (Manual)\n";
            $content .= "item debug Install Proxmox VE {$version} (Debug)\n";
            $content .= "choose --default automated --timeout \${timeout} target && goto \${target}\n";
            $content .= "\n";
            
            $content .= ":automated\n";
            $content .= "kernel \${base-url}/linux26 vga=791 video=vesafb:ywrap,mtrr ramdisk_size=16777216 rw quiet splash=silent proxmox-start-auto-installer\n";
            $content .= "initrd \${base-url}/initrd.img\n";
            $content .= "boot\n";
            $content .= "\n";
            
            $content .= ":manual\n";
            $content .= "kernel \${base-url}/linux26 vga=791 video=vesafb:ywrap,mtrr ramdisk_size=16777216 rw quiet splash=silent\n";
            $content .= "initrd \${base-url}/initrd.img\n";
            $content .= "boot\n";
            $content .= "\n";
            
            $content .= ":debug\n";
            $content .= "kernel \${base-url}/linux26 vga=791 video=vesafb:ywrap,mtrr ramdisk_size=16777216 rw quiet splash=verbose proxdebug\n";
            $content .= "initrd \${base-url}/initrd.img\n";
            $content .= "boot\n";
        } else {
            // Proxmox VE 7.x - no automated installation, use preseed approach
            $content .= "# Proxmox VE {$version} with preseed configuration\n";
            $content .= "kernel \${base-url}/linux26 vga=791 video=vesafb:ywrap,mtrr ramdisk_size=16777216 rw auto=true priority=critical preseed/url=http://auto.x-zoneit.ro/servers/{$servername}/preseed.cfg\n";
            $content .= "initrd \${base-url}/initrd.img\n";
            $content .= "boot\n";
        }
        
        return $content;
    }
    
    /**
     * Generate meta-data content
     */
    private function generateMetaData($hostname) {
        $content = "instance-id: $hostname\n";
        $content .= "local-hostname: $hostname\n";
        
        return $content;
    }
    
        /**
     * Generate user-data content (cloud-init)
     */
    private function generateUserData($network_config, $custom_password = null, $cleanup_info = [], $server_data = []) {
        $hostname = $network_config['hostname'];
        $ip_address = $network_config['ip_address'];
        $subnet_mask = $network_config['subnet_mask'];
        $gateway = $network_config['gateway'];
        $dns_primary = $network_config['dns_primary'];
        $mac_address = $network_config['mac'] ?? ''; // may be empty

        // Calculate CIDR from subnet mask
        $cidr = $this->subnetMaskToCIDR($subnet_mask);

        // Generate random password and store in database
        $random_password = $this->generateRandomPassword();
        $password_hash = password_hash($random_password, PASSWORD_DEFAULT);
        
        // Store password in appropriate database table based on server_id
        $server_id = $cleanup_info['server_id'] ?? null;
        if ($server_id) {
            $this->storeServerPassword($server_id, $random_password);
        }

        // Calculate all usable IP addresses from main_ip and additional_ips
        $all_usable_ips = $this->getAllUsableIPs($server_data);
        
        $content = "#cloud-config\n";
        $content .= "autoinstall:\n";
        $content .= "  version: 1\n";
        $content .= "  identity:\n";
        $content .= "    hostname: $hostname\n";
        $content .= "    username: user\n";
        $content .= "    password: $password_hash\n";
        $content .= "  early-commands:\n";
        $content .= "    - systemctl stop ssh\n";
        // Top-level network configuration (outside autoinstall)\n";
        $content .= "  network:\n";
        $content .= "    network:\n";
        $content .= "      version: 2\n";
        $content .= "      ethernets:\n";
        $content .= "        eth0:\n";
        $content .= "          match:\n";
        $content .= "            macaddress: \"$mac_address\"\n";
        $content .= "          addresses:\n";
        
        // Add all usable IP addresses
        if (!empty($all_usable_ips)) {
            foreach ($all_usable_ips as $ip_with_cidr) {
                $content .= "            - $ip_with_cidr\n";
            }
        } else {
            // Fallback to original single IP if no usable IPs found
            $content .= "            - $ip_address/$cidr\n";
        }
        
        $content .= "          gateway4: $gateway\n";
        $content .= "          nameservers:\n";
        $content .= "            addresses: [$dns_primary]\n";
        $content .= "  ssh:\n";
        $content .= "    install-server: true\n";
        $content .= "    allow-pw: true\n";
        $content .= "  storage:\n";
        $content .= "    layout:\n";
        $content .= "      name: lvm\n";
        $content .= "    swap:\n";
        $content .= "      size: 0\n";
        $content .= "  packages:\n";
        $content .= "    - openssh-server\n";
        $content .= "    - net-tools\n";
        $content .= "    - curl\n";
        $content .= "    - wget\n";
        $content .= "  late-commands:\n";
        $content .= "    - sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet splash\"/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet splash net.ifnames=0 biosdevname=0\"/' /target/etc/default/grub\n";
        $content .= "    - curtin in-target --target=/target -- update-grub\n";
        $content .= "  user-data:\n";
        $content .= "    runcmd:\n";
        $content .= "      # Set root password\n";
        $content .= "      - echo 'root:$password_hash' | chpasswd --encrypted\n";
        $content .= "      \n";
        $content .= "      # Configure SSH for root access\n";
        $content .= "      - sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config\n";
        $content .= "      - sed -i 's/PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config\n";
        $content .= "      \n";
        $content .= "      # Restart SSH service\n";
        $content .= "      - systemctl restart ssh\n";

        // -------------------------------------------------------
        // Notify backend that installation is complete so that
        // DHCP entries and autoinstall files get cleaned up
        // -------------------------------------------------------
        if (!empty($cleanup_info)) {
            $cleanup_payload = json_encode(array_filter([
                'server_id'  => $cleanup_info['server_id'] ?? null,
                'hostname'   => $cleanup_info['hostname'] ?? null,
                'session_id' => $cleanup_info['session_id'] ?? null,
                'servername' => $cleanup_info['servername'] ?? null
            ]));

            // Escape for inclusion in single-quoted string inside YAML
            $escaped_payload = str_replace("'", "'\\''", $cleanup_payload);

            $content .= "      - |\n";
            $content .= "        curl -sS -k -X POST -H 'Content-Type: application/json' --data '$escaped_payload' https://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup || true\n";
        }

        // End of runcmd section

        return $content;
    }
    
    /**
     * Get OS path based on template
     */
    private function getOSPath($os_template) {
        // Kept for backward-compatibility but superseded by getOSConfig()
        $mapping = $this->getOSConfig($os_template);
        return $mapping['path'] ?? 'ubuntu-22.04';
    }
    
    /**
     * Convert subnet mask to CIDR notation
     */
    private function subnetMaskToCIDR($subnet_mask) {
        $long = ip2long($subnet_mask);
        $base = ip2long('***************');
        return 32 - log(($long ^ $base) + 1, 2);
    }
    
    /**
     * Generate a random password
     */
    public function generateRandomPassword($length = 16) {
        // Character pools
        $upper   = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lower   = 'abcdefghijklmnopqrstuvwxyz';
        $digits  = '0123456789';
        // Only pick symbols that are safe in kick-start/plain files and JSON/YAML to avoid escaping issues
        $special = '!@*()=';

        $all = $upper . $lower . $digits . $special;
        $allLength = strlen($all);

        // Generate until we satisfy ESXi complexity (≥3 of the 4 classes)
        do {
            $password = '';
            for ($i = 0; $i < $length; $i++) {
                $password .= $all[random_int(0, $allLength - 1)];
            }

            $classes = 0;
            $classes += (int)preg_match('/[A-Z]/', $password); // upper
            $classes += (int)preg_match('/[a-z]/', $password); // lower
            $classes += (int)preg_match('/[0-9]/', $password); // digit
            $classes += (int)preg_match('/[!@#$%^&*()\-_=+]/', $password); // special

        } while ($classes < 3);

        return $password;
    }
    
    /**
     * Store server password in appropriate database table based on server_id
     */
    private function storeServerPassword($server_id, $password) {
        try {
            if ($server_id >= 1000000) {
                // Store in inventory_dedicated_servers
                $stmt = $this->pdo->prepare("UPDATE inventory_dedicated_servers SET password = ? WHERE id = ?");
                $table = 'inventory_dedicated_servers';
            } else {
                // Store in blade_server_inventory
                $stmt = $this->pdo->prepare("UPDATE blade_server_inventory SET password = ? WHERE id = ?");
                $table = 'blade_server_inventory';
            }
            
            $stmt->execute([$password, $server_id]);
            
            $this->logOperation("Password stored successfully", [
                'server_id' => $server_id,
                'table' => $table,
                'password_length' => strlen($password)
            ]);
            
        } catch (Exception $e) {
            $this->logOperation("Failed to store password: " . $e->getMessage(), [
                'server_id' => $server_id,
                'error' => $e->getMessage()
            ]);
            // Don't re-throw the exception, just log it
        }
    }
    
    /**
     * Get all usable IP addresses from server main_ip and additional_ips
     * Excludes network, gateway, and broadcast addresses
     */
    private function getAllUsableIPs($server_data) {
        $usable_ips = [];
        
        $this->logOperation("Processing server data for usable IPs", [
            'main_ip' => $server_data['main_ip'] ?? 'none',
            'additional_ips' => $server_data['additional_ips'] ?? 'none'
        ]);
        
        // Process main_ip if it exists
        if (!empty($server_data['main_ip'])) {
            $main_ips = $this->getUsableIPsFromCIDR($server_data['main_ip']);
            $usable_ips = array_merge($usable_ips, $main_ips);
        }
        
        // Process additional_ips if they exist
        if (!empty($server_data['additional_ips'])) {
            $additional_cidrs = explode(',', $server_data['additional_ips']);
            foreach ($additional_cidrs as $cidr) {
                $cidr = trim($cidr);
                if (!empty($cidr)) {
                    $additional_ips = $this->getUsableIPsFromCIDR($cidr);
                    $usable_ips = array_merge($usable_ips, $additional_ips);
                }
            }
        }
        
        // Remove duplicates and return
        $final_ips = array_unique($usable_ips);
        
        $this->logOperation("Final usable IPs calculated", [
            'total_count' => count($final_ips),
            'ips' => $final_ips
        ]);
        
        return $final_ips;
    }
    
    /**
     * Extract usable IP addresses from a CIDR block
     * Excludes network address, gateway (network+1), and broadcast address
     */
    private function getUsableIPsFromCIDR($cidr) {
        $usable_ips = [];
        
        // Parse CIDR notation
        if (strpos($cidr, '/') === false) {
            // If no CIDR, assume /32 (single IP)
            return [$cidr . '/32'];
        }
        
        list($network_ip, $prefix) = explode('/', $cidr);
        $prefix = (int)$prefix;
        
        // Calculate network boundaries
        $network_long = ip2long($network_ip);
        $mask = -1 << (32 - $prefix);
        $network_long = $network_long & $mask;
        $broadcast_long = $network_long | (~$mask & 0xFFFFFFFF);
        
        // Gateway is typically network + 1
        $gateway_long = $network_long + 1;
        
        // For very small subnets (/31, /32), handle specially
        if ($prefix >= 31) {
            // /31 has 2 IPs, both usable (no network/broadcast)
            // /32 has 1 IP, which is usable
            for ($ip_long = $network_long; $ip_long <= $broadcast_long; $ip_long++) {
                $usable_ips[] = long2ip($ip_long) . "/$prefix";
            }
        } else {
            // Standard subnet: exclude network, gateway, and broadcast
            for ($ip_long = $network_long + 2; $ip_long < $broadcast_long; $ip_long++) {
                // Skip gateway IP if it falls in this range
                if ($ip_long !== $gateway_long) {
                    $usable_ips[] = long2ip($ip_long) . "/$prefix";
                }
            }
        }
        
        $this->logOperation("Extracted usable IPs from CIDR", [
            'cidr' => $cidr,
            'usable_count' => count($usable_ips),
            'usable_ips' => $usable_ips
        ]);
        
        return $usable_ips;
    }
    
    /**
     * Extract server name from hostname
     */
    public function extractServerName($hostname) {
        // Extract server name (remove domain part)
        $parts = explode('.', $hostname);
        return $parts[0];
    }
    
    /**
     * Clean up PXE configuration after installation
     */
    public function cleanupPXEConfig($server_id, $servername, $hostname) {
        try {
            // Remove DHCP entry
            if (file_exists($this->config['dhcp_config_file'])) {
                $dhcp_content = file_get_contents($this->config['dhcp_config_file']);
                $dhcp_content = $this->removeDHCPEntry($dhcp_content, $servername);
                file_put_contents($this->config['dhcp_config_file'], $dhcp_content);
                
                // Restart DHCP service
                exec('sudo systemctl restart isc-dhcp-server 2>&1');
            }
            
            // Remove autoinstall directory
            $server_dir = $this->config['autoinstall_root'] . "/servers/$servername";
            if (is_dir($server_dir)) {
                // Remove the server directory and all files
                exec("rm -rf " . escapeshellarg($server_dir));
                $this->logOperation("Removed autoinstall directory", ['server_dir' => $server_dir]);
            }
            
            $this->logOperation("PXE configuration cleaned up", [
                'server_id' => $server_id,
                'hostname' => $hostname,
                'servername' => $servername
            ]);
            
            return ['success' => true, 'message' => 'PXE configuration cleaned up'];
            
        } catch (Exception $e) {
            $this->logOperation("PXE cleanup failed: " . $e->getMessage(), ['server_id' => $server_id]);
            throw $e;
        }
    }
    
    /**
     * Legacy methods for backward compatibility
     */
    public function createDHCPReservation($server_id, $mac_address, $network_config, $dhcp_config) {
        $server_data = ['mac' => $mac_address];
        return $this->setupCompleteReinstall($server_id, $server_data, $network_config, 'ubuntu-22.04-server');
    }
    
    public function setupPXEServer($server_id, $mac_address, $network_config, $os_template) {
        $server_data = ['mac' => $mac_address];
        return $this->setupCompleteReinstall($server_id, $server_data, $network_config, $os_template);
    }
    
    /**
     * Utility functions
     */
    
    private function validateNetworkConfig($config) {
        $required = ['ip_address', 'subnet_mask', 'gateway', 'hostname', 'dns_primary'];
        
        foreach ($required as $field) {
            if (empty($config[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }
        
        if (!filter_var($config['ip_address'], FILTER_VALIDATE_IP)) {
            throw new Exception("Invalid IP address");
        }
        
        if (!filter_var($config['gateway'], FILTER_VALIDATE_IP)) {
            throw new Exception("Invalid gateway address");
        }
        
        if (!filter_var($config['dns_primary'], FILTER_VALIDATE_IP)) {
            throw new Exception("Invalid primary DNS address");
        }
        
        if (!preg_match('/^[a-zA-Z0-9.-]+$/', $config['hostname'])) {
            throw new Exception("Invalid hostname format");
        }
    }
    
    private function formatMacAddress($mac) {
        $mac = preg_replace('/[^0-9A-Fa-f]/', '', $mac);
        
        if (strlen($mac) !== 12) {
            return false;
        }
        
        return strtolower(implode(':', str_split($mac, 2)));
    }
    
    private function logOperation($message, $data = []) {
        try {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'data' => $data
        ];
        
        $log_line = json_encode($log_entry) . "\n";
            
            // Try to write to log file, but don't fail if it doesn't work
            @file_put_contents($this->config['log_file'], $log_line, FILE_APPEND | LOCK_EX);
            
            // Also log to auto.logs as fallback
            error_log("PXE: $message - " . json_encode($data), 3, "auto.logs");
            
        } catch (Exception $e) {
            // If logging fails, at least try to use auto.logs
            error_log("PXE logging failed: " . $e->getMessage(), 3, "auto.logs");
            error_log("PXE: $message - " . json_encode($data), 3, "auto.logs");
        }
    }

    /**
     * Extended OS configuration helper. All parameters are best-effort defaults – adjust paths/files on the PXE repository if needed.
     */
    private function getOSConfig($os_template) {
        // ---------------------------------------------
        // Normalise template name to mapping key
        // ---------------------------------------------
        $key = strtolower(trim($os_template));          // e.g. "Ubuntu 20.04 Server" → "ubuntu 20.04 server"
        $key = str_replace(['  ', ' '], '-', $key);     // spaces → dashes  (multiple collapses ok)

        // for Ubuntu entries that do not contain "server" explicitly, append it so both
        // "ubuntu-22.04" and "ubuntu-22.04-server" resolve to same config
        if (strpos($key, 'ubuntu-') === 0 && strpos($key, '-server') === false) {
            $key .= '-server';
        }

        // Common Ubuntu autoinstall kernel params
        $ubuntuBoot = 'autoinstall ds=nocloud-net;s=http://auto.x-zoneit.ro/servers/%SERVER%/';

        $cfg = [
            // Ubuntu family ---------------------------------------------------
            'ubuntu-18.04-server' => [
                'path'        => 'ubuntu-18.04',
                'iso'         => 'ubuntu-18.04.6-live-server-amd64.iso',
                'kernel'      => 'linux',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            'ubuntu-19.04-server' => [
                'path'        => 'ubuntu-19.04',
                'iso'         => 'ubuntu-19.04-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-20.04-server' => [
                'path'        => 'ubuntu-20.04',
                'iso'         => 'ubuntu-20.04.6-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-21.04-server' => [
                'path'        => 'ubuntu-21.04',
                'iso'         => 'ubuntu-21.04-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-22.04-server' => [
                'path'        => 'ubuntu-22.04',
                'iso'         => 'ubuntu-22.04.5-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-23.04-server' => [
                'path'        => 'ubuntu-23.04',
                'iso'         => 'ubuntu-23.04-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-24.04-server' => [
                'path'        => 'ubuntu-24.04',
                'iso'         => 'ubuntu-24.04.2-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],

            // Debian family ---------------------------------------------------
            'debian-12' => [
                'path'        => 'debian-12',
                'iso'         => 'debian-12.11.0-amd64-netinst.iso',
                'kernel'      => 'linux',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'auto url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg',
            ],
            'debian-11' => [
                'path'        => 'debian-11',
                'iso'         => 'debian-11.7.0-amd64-netinst.iso',
                'kernel'      => 'linux',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'auto url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg',
            ],
            'debian-10' => [
                'path'        => 'debian-10',
                'iso'         => 'debian-10.13.0-amd64-netinst.iso',
                'kernel'      => 'linux',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'auto url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg',
            ],

            // CentOS family ---------------------------------------------------
            'centos-9' => [
                'path'        => 'centos-9',
                'iso'         => '', // Using network install tree, ISO not mandatory
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd.img',
                'boot_params' => 'inst.ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg inst.repo=http://auto.x-zoneit.ro/os/centos-9/ inst.stage2=http://auto.x-zoneit.ro/os/centos-9/',
            ],
            'centos-10' => [
                'path'        => 'centos-10',
                'iso'         => '', // Using network install tree, ISO not mandatory
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd.img',
                'boot_params' => 'inst.ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg inst.repo=http://auto.x-zoneit.ro/os/centos-10/ inst.stage2=http://auto.x-zoneit.ro/os/centos-10/',
            ],

            // AlmaLinux family ------------------------------------------------
            'almalinux-8' => [
                'path'        => 'almalinux-8',
                'iso'         => '', // Using network install tree, ISO not mandatory
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd.img',
                'boot_params' => 'inst.ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg inst.repo=http://auto.x-zoneit.ro/os/almalinux-8/ inst.stage2=http://auto.x-zoneit.ro/os/almalinux-8/',
            ],
            'almalinux-9' => [
                'path'        => 'almalinux-9',
                'iso'         => '', // Using network install tree, ISO not mandatory
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd.img',
                'boot_params' => 'inst.ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg inst.repo=http://auto.x-zoneit.ro/os/almalinux-9/ inst.stage2=http://auto.x-zoneit.ro/os/almalinux-9/',
            ],
            'almalinux-10' => [
                'path'        => 'almalinux-10',
                'iso'         => '', // Using network install tree, ISO not mandatory
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd.img',
                'boot_params' => 'inst.ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg inst.repo=http://auto.x-zoneit.ro/os/almalinux-10/ inst.stage2=http://auto.x-zoneit.ro/os/almalinux-10/',
            ],

            // VMware ESXi family ----------------------------------------------
            'esxi-7.0' => [
                'path'        => 'esxi-7.0',
                'iso'         => '', // Using extracted ESXi files
                'kernel'      => 'mboot.c32',
                'initrd'      => '', // ESXi uses mboot.c32 with boot.cfg
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            'esxi-8.0' => [
                'path'        => 'esxi-8.0',
                'iso'         => '', // Using extracted ESXi files
                'kernel'      => 'mboot.c32',
                'initrd'      => '', // ESXi uses mboot.c32 with boot.cfg
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            'esxi-9.0' => [
                'path'        => 'esxi-9.0',
                'iso'         => '', // Using extracted ESXi files
                'kernel'      => 'mboot.c32',
                'initrd'      => '', // ESXi uses mboot.c32 with boot.cfg
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],

            // --- Aliases (without "-server" suffix) ---------------------
            'ubuntu-18.04' => [
                'path'        => 'ubuntu-18.04',
                'iso'         => 'ubuntu-18.04.6-live-server-amd64.iso',
                'kernel'      => 'linux',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            'ubuntu-19.04' => [
                'path'        => 'ubuntu-19.04',
                'iso'         => 'ubuntu-19.04-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-20.04' => [
                'path'        => 'ubuntu-20.04',
                'iso'         => 'ubuntu-20.04.6-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-21.04' => [
                'path'        => 'ubuntu-21.04',
                'iso'         => 'ubuntu-21.04-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-22.04' => [
                'path'        => 'ubuntu-22.04',
                'iso'         => 'ubuntu-22.04.5-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-23.04' => [
                'path'        => 'ubuntu-23.04',
                'iso'         => 'ubuntu-23.04-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            'ubuntu-24.04' => [
                'path'        => 'ubuntu-24.04',
                'iso'         => 'ubuntu-24.04-live-server-amd64.iso',
                'kernel'      => 'vmlinuz',
                'initrd'      => 'initrd',
                'boot_params' => $ubuntuBoot,
            ],
            
            // VMware ESXi aliases ---------------------------------------------
            'esxi-7' => [
                'path'        => 'esxi-7.0',
                'iso'         => '', 
                'kernel'      => 'mboot.c32',
                'initrd'      => '',
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            'esxi-8' => [
                'path'        => 'esxi-8.0',
                'iso'         => '', 
                'kernel'      => 'mboot.c32',
                'initrd'      => '',
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            'esxi-9' => [
                'path'        => 'esxi-9.0',
                'iso'         => '', 
                'kernel'      => 'mboot.c32',
                'initrd'      => '',
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            'vmware-esxi-7' => [
                'path'        => 'esxi-7.0',
                'iso'         => '', 
                'kernel'      => 'mboot.c32',
                'initrd'      => '',
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            'vmware-esxi-8' => [
                'path'        => 'esxi-8.0',
                'iso'         => '', 
                'kernel'      => 'mboot.c32',
                'initrd'      => '',
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            'vmware-esxi-9' => [
                'path'        => 'esxi-9.0',
                'iso'         => '', 
                'kernel'      => 'mboot.c32',
                'initrd'      => '',
                'boot_params' => 'ks=http://auto.x-zoneit.ro/servers/%SERVER%/ks.cfg',
            ],
            
            // Proxmox VE family ----------------------------------------------
            'proxmox-7.4' => [
                'path'        => 'proxmox-7.4',
                'iso'         => 'proxmox-ve_7.4-1.iso',
                'kernel'      => 'linux26',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            'proxmox-8' => [
                'path'        => 'proxmox-8',
                'iso'         => '',
                'kernel'      => 'linux26',
                'initrd'      => 'initrd.img',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            'proxmox-9.0' => [
                'path'        => 'proxmox-9.0',
                'iso'         => 'proxmox-ve_9.0-1.iso',
                'kernel'      => 'linux26',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            
            // Proxmox VE aliases ---------------------------------------------
            'proxmox-7' => [
                'path'        => 'proxmox-7.4',
                'iso'         => 'proxmox-ve_7.4-1.iso',
                'kernel'      => 'linux26',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            'proxmox-8' => [
                'path'        => 'proxmox-8',
                'iso'         => '',
                'kernel'      => 'linux26',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            'proxmox-9' => [
                'path'        => 'proxmox-9.0',
                'iso'         => 'proxmox-ve_9.0-1.iso',
                'kernel'      => 'linux26',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
            'proxmox-ve' => [
                'path'        => 'proxmox-8',
                'iso'         => '',
                'kernel'      => 'linux26',
                'initrd'      => 'initrd.gz',
                'boot_params' => 'preseed/url=http://auto.x-zoneit.ro/servers/%SERVER%/preseed.cfg auto=true priority=critical netcfg/choose_interface=auto netcfg/disable_dhcp=false ip=dhcp',
            ],
        ];

        // Fallback to Ubuntu 22.04 if unknown
        return $cfg[$key] ?? $cfg['ubuntu-22.04-server'];
    }

/**
 * Generate a minimal Kickstart file for CentOS/RHEL/AlmaLinux-derived distributions.
 * Updated to use NetworkManager with nmcli for proper multiple IP configuration
 */
private function generateKickstart($network_config, $plain_password = null, $cleanup_info = [], $server_data = []) {
    $pw = $plain_password ?? $this->generateRandomPassword();
    $pw_hash = crypt($pw, '$1$' . substr(md5(rand()), 0, 8) . '$');
    $server_id = $cleanup_info['server_id'] ?? null;
    
    if ($server_id) {
        $this->storeServerPassword($server_id, $pw);
    }

    $content  = "#version=RHEL9\n";
    $content .= "text\n";
    $content .= "firstboot --enable\n";
    $content .= "ignoredisk --only-use=sda\n";
    $content .= "keyboard --vckeymap=us --xlayouts='us'\n";
    $content .= "lang en_US.UTF-8\n\n";
    $content .= "network --device=eth0 --bootproto=static --ip={$network_config['ip_address']} --netmask={$network_config['subnet_mask']} --gateway={$network_config['gateway']} --nameserver={$network_config['dns_primary']} --hostname={$network_config['hostname']} --activate\n";
    $content .= "network --hostname={$network_config['hostname']}\n\n";
    $content .= "rootpw --iscrypted {$pw_hash}\n";
    $content .= "services --enabled=\"NetworkManager,chronyd,sshd\"\n";
    $content .= "timezone UTC\n";
    $content .= "user --groups=wheel --name=user --password={$pw_hash} --iscrypted --gecos=\"Default User\"\n";
    $content .= "bootloader --location=mbr --boot-drive=sda\n";
    $content .= "clearpart --all --initlabel --drives=sda\n";
    $content .= "part /boot --fstype=xfs --size=1024\n";
    $content .= "part /boot/efi --fstype=efi --size=200 --ondisk=sda\n";
    $content .= "part / --fstype=xfs --size=10240 --grow\n";
    $content .= "eula --agreed\n";
    $content .= "reboot\n\n";
    $content .= "%packages --ignoremissing\n";
    $content .= "@^minimal-environment\n";
    $content .= "NetworkManager\n";
    $content .= "NetworkManager-tui\n";
    $content .= "openssh-server\n";
    $content .= "net-tools\n";
    $content .= "curl\n";
    $content .= "wget\n";
    $content .= "%end\n\n";
    $content .= "%post --log=/root/ks-post.log\n";
    $content .= "chage -d \$(date +%Y-%m-%d) -M 99999 -W 7 -I -1 root\n";
    $content .= "chage -d \$(date +%Y-%m-%d) -M 99999 -W 7 -I -1 user\n";
    $content .= "passwd -x 99999 root\n";
    $content .= "passwd -x 99999 user\n";
    $content .= "passwd -w 7 root\n";
    $content .= "passwd -w 7 user\n";
    $content .= "passwd -u root 2>/dev/null || true\n";
    $content .= "passwd -u user 2>/dev/null || true\n";
    $content .= "chage -d \$((\$(date +%s) / 86400)) root 2>/dev/null || true\n";
    $content .= "chage -d \$((\$(date +%s) / 86400)) user 2>/dev/null || true\n";
    $content .= "sed -i 's/#PermitRootLogin yes/PermitRootLogin yes/' /etc/ssh/sshd_config\n";
    $content .= "sed -i 's/PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config\n";
    $content .= "sed -i 's/PermitRootLogin no/PermitRootLogin yes/' /etc/ssh/sshd_config\n";
    $content .= "sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config\n";
    $content .= "sed -i 's/PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config\n";
    $content .= "grep -q '^PermitRootLogin yes' /etc/ssh/sshd_config || echo 'PermitRootLogin yes' >> /etc/ssh/sshd_config\n";
    $content .= "grep -q '^PasswordAuthentication yes' /etc/ssh/sshd_config || echo 'PasswordAuthentication yes' >> /etc/ssh/sshd_config\n";
    $content .= "systemctl enable sshd\n";
    $content .= "systemctl start sshd\n";
    $content .= "sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config\n";
    $content .= "sed -i 's/GRUB_CMDLINE_LINUX=\"\"/GRUB_CMDLINE_LINUX=\"net.ifnames=0 biosdevname=0\"/' /etc/default/grub\n";
    $content .= "grub2-mkconfig -o /boot/grub2/grub.cfg\n";
    
    // Network setup log
    $content .= "touch /var/log/network-setup.log\n";
    $content .= "echo \"Starting network configuration at \$(date)\" > /var/log/network-setup.log\n\n";
    $content .= "echo \"Main IP config: {$network_config['ip_address']}/{$this->subnetMaskToCIDR($network_config['subnet_mask'])}\" >> /var/log/network-setup.log\n";
    
    // Server data debug
    $content .= "cat > /root/server_data.txt << 'EOF'\n";
    $content .= "Main IP: " . ($server_data['main_ip'] ?? 'not set') . "\n";
    $content .= "Additional IPs: " . ($server_data['additional_ips'] ?? 'none') . "\n";
    $content .= "EOF\n\n";
    
    // Get all usable IPs
    $all_usable_ips = $this->getAllUsableIPs($server_data);
    
    // Log IPs
    $ip_index = 0;
    foreach ($all_usable_ips as $ip_cidr) {
        $content .= "echo \"Found IP $ip_index: $ip_cidr\" >> /var/log/network-setup.log\n";
        $ip_index++;
    }
    $content .= "echo \"Total IPs to configure: $ip_index\" >> /var/log/network-setup.log\n\n";
    
    // MAC address handling
    $mac_address = $network_config['mac'] ?? '';
    $content .= "# Dynamic interface detection\n";
    $content .= "echo \"Creating NetworkManager keyfile configuration...\" >> /var/log/network-setup.log\n\n";
    
    if (!empty($mac_address)) {
        $content .= "# Find interface by MAC address\n";
        $content .= "MAC_ADDRESS=\"{$mac_address}\"\n";
        $content .= "INTERFACE_NAME=\"\"\n";
        $content .= "echo \"Looking for interface with MAC: \$MAC_ADDRESS\" >> /var/log/network-setup.log\n\n";
        
        $content .= "for iface in /sys/class/net/*; do\n";
        $content .= "    if [ -f \"\$iface/address\" ]; then\n";
        $content .= "        iface_mac=\$(cat \"\$iface/address\" 2>/dev/null)\n";
        $content .= "        iface_name=\$(basename \"\$iface\")\n";
        $content .= "        echo \"Checking \$iface_name: \$iface_mac\" >> /var/log/network-setup.log\n";
        $content .= "        if [ \"\${iface_mac,,}\" = \"\${MAC_ADDRESS,,}\" ]; then\n";
        $content .= "            INTERFACE_NAME=\"\$iface_name\"\n";
        $content .= "            echo \"Found matching interface: \$INTERFACE_NAME\" >> /var/log/network-setup.log\n";
        $content .= "            break\n";
        $content .= "        fi\n";
        $content .= "    fi\n";
        $content .= "done\n\n";
        
        $content .= "if [ -z \"\$INTERFACE_NAME\" ]; then\n";
        $content .= "    echo \"WARNING: MAC match failed, using first non-loopback\" >> /var/log/network-setup.log\n";
        $content .= "    INTERFACE_NAME=\$(ip -o link show | grep -v loopback | head -n1 | awk '{print \$2}' | sed 's/:$//')\n";
        $content .= "    echo \"Fallback interface: \$INTERFACE_NAME\" >> /var/log/network-setup.log\n";
        $content .= "fi\n\n";
    } else {
        $content .= "# No MAC provided, using first non-loopback\n";
        $content .= "INTERFACE_NAME=\$(ip -o link show | grep -v loopback | head -n1 | awk '{print \$2}' | sed 's/:$//')\n";
        $content .= "echo \"Using interface: \$INTERFACE_NAME\" >> /var/log/network-setup.log\n\n";
    }
    
    // Generate UUID
    $content .= "CONNECTION_UUID=\$(uuidgen)\n";
    $content .= "echo \"Generated UUID: \$CONNECTION_UUID\" >> /var/log/network-setup.log\n\n";
    
    // Create keyfile
    if (!empty($all_usable_ips)) {
        $content .= "mkdir -p /etc/NetworkManager/system-connections/\n";
        $content .= "cat > /etc/NetworkManager/system-connections/network.nmconnection << 'NMEOF'\n";
        $content .= "[connection]\n";
        $content .= "id=network\n";
        $content .= "uuid=\$CONNECTION_UUID\n";
        $content .= "type=ethernet\n";
        $content .= "autoconnect=true\n";
        
        if (!empty($mac_address)) {
            $content .= "[ethernet]\n";
            $content .= "mac-address={$mac_address}\n";
        } else {
            $content .= "interface-name=\$INTERFACE_NAME\n";
            $content .= "[ethernet]\n";
        }
        
        $content .= "\n";
        $content .= "[ipv4]\n";
        $content .= "method=manual\n";
        
        // Add IP addresses
        $address_index = 1;
        foreach ($all_usable_ips as $ip_cidr) {
            list($ip, $cidr) = explode('/', $ip_cidr);
            if ($ip == $network_config['ip_address']) {
                $content .= "address{$address_index}={$ip}/{$cidr},{$network_config['gateway']}\n";
            } else {
                $content .= "address{$address_index}={$ip}/{$cidr}\n";
            }
            $address_index++;
        }
        
        // Add DNS
        $content .= "dns={$network_config['dns_primary']}";
        if (!empty($network_config['dns_secondary'])) {
            $content .= ";{$network_config['dns_secondary']}";
        }
        $content .= "\n";
        
        $content .= "[ipv6]\n";
        $content .= "method=auto\n";
        $content .= "NMEOF\n\n";
        
        // Replace placeholders
        $content .= "sed -i \"s/\\\$CONNECTION_UUID/\$CONNECTION_UUID/g\" /etc/NetworkManager/system-connections/network.nmconnection\n";
        $content .= "sed -i \"s/\\\$INTERFACE_NAME/\$INTERFACE_NAME/g\" /etc/NetworkManager/system-connections/network.nmconnection\n\n";
        
        // Set permissions
        $content .= "chmod 600 /etc/NetworkManager/system-connections/network.nmconnection\n";
        $content .= "chown root:root /etc/NetworkManager/system-connections/network.nmconnection\n\n";
        
        // Start NetworkManager
        $content .= "systemctl enable NetworkManager\n";
        $content .= "systemctl start NetworkManager\n\n";
        $content .= "nmcli connection reload >> /var/log/network-setup.log 2>&1\n\n";
        
        // Wait for NetworkManager
        $content .= "for i in {1..30}; do\n";
        $content .= "    if nmcli general status >/dev/null 2>&1; then\n";
        $content .= "        echo \"NetworkManager ready\" >> /var/log/network-setup.log\n";
        $content .= "        break\n";
        $content .= "    fi\n";
        $content .= "    echo \"Waiting for NetworkManager... \$i/30\" >> /var/log/network-setup.log\n";
        $content .= "    sleep 2\n";
        $content .= "done\n\n";
        
        // Activate connection
        $content .= "nmcli connection up network >> /var/log/network-setup.log 2>&1 || true\n\n";
        
        // Log configuration
        $content .= "echo \"=== NetworkManager config ===\" >> /var/log/network-setup.log\n";
        $content .= "cat /etc/NetworkManager/system-connections/network.nmconnection >> /var/log/network-setup.log\n";
        $content .= "echo \"=== End of config ===\" >> /var/log/network-setup.log\n\n";
        
        // Create verification script
        $content .= "cat > /usr/local/bin/verify-network.sh << 'EOFSCRIPT'\n";
        $content .= "#!/bin/bash\n\n";
        $content .= "LOG_FILE=/var/log/network-setup.log\n";
        $content .= "echo \"=== Network verification: \$(date) ===\" >> \$LOG_FILE\n\n";
        
        // Interface detection in verification script
        if (!empty($mac_address)) {
            $content .= "MAC_ADDRESS=\"{$mac_address}\"\n";
            $content .= "INTERFACE_NAME=\"\"\n";
            $content .= "for iface in /sys/class/net/*; do\n";
            $content .= "    if [ -f \"\$iface/address\" ]; then\n";
            $content .= "        iface_mac=\$(cat \"\$iface/address\" 2>/dev/null)\n";
            $content .= "        if [ \"\${iface_mac,,}\" = \"\${MAC_ADDRESS,,}\" ]; then\n";
            $content .= "            INTERFACE_NAME=\$(basename \"\$iface\")\n";
            $content .= "            break\n";
            $content .= "        fi\n";
            $content .= "    fi\n";
            $content .= "done\n\n";
            $content .= "if [ -z \"\$INTERFACE_NAME\" ]; then\n";
            $content .= "    INTERFACE_NAME=\$(ip -o link show | grep -v loopback | head -n1 | awk '{print \$2}' | sed 's/:$//')\n";
            $content .= "fi\n\n";
        } else {
            $content .= "INTERFACE_NAME=\$(ip -o link show | grep -v loopback | head -n1 | awk '{print \$2}' | sed 's/:$//')\n\n";
        }
        
        $content .= "echo \"Verifying interface: \$INTERFACE_NAME\" >> \$LOG_FILE\n\n";
        $content .= "if [ ! -f /etc/NetworkManager/system-connections/network.nmconnection ]; then\n";
        $content .= "    echo \"ERROR: Keyfile missing\" >> \$LOG_FILE\n";
        $content .= "    exit 1\n";
        $content .= "fi\n\n";
        
        $content .= "if ! nmcli connection show --active | grep -q network; then\n";
        $content .= "    echo \"Activating network connection\" >> \$LOG_FILE\n";
        $content .= "    nmcli connection up network >> \$LOG_FILE 2>&1\n";
        $content .= "fi\n\n";
        
        $content .= "MISSING_IPS=0\n";
        foreach ($all_usable_ips as $ip_cidr) {
            list($ip, $cidr) = explode('/', $ip_cidr);
            $content .= "if ! ip addr show dev \$INTERFACE_NAME | grep -q '$ip'; then\n";
            $content .= "    echo \"MISSING: $ip\" >> \$LOG_FILE\n";
            $content .= "    MISSING_IPS=1\n";
            $content .= "else\n";
            $content .= "    echo \"OK: $ip\" >> \$LOG_FILE\n";
            $content .= "fi\n\n";
        }
        
        $content .= "echo \"=== Final config ===\" >> \$LOG_FILE\n";
        $content .= "ip addr show \$INTERFACE_NAME >> \$LOG_FILE\n";
        $content .= "echo \"=== NetworkManager details ===\" >> \$LOG_FILE\n";
        $content .= "nmcli connection show network >> \$LOG_FILE\n";
        $content .= "echo \"=== Routing table ===\" >> \$LOG_FILE\n";
        $content .= "ip route >> \$LOG_FILE\n";
        $content .= "echo \"Verification completed\" >> \$LOG_FILE\n";
        $content .= "EOFSCRIPT\n\n";
        
        $content .= "chmod +x /usr/local/bin/verify-network.sh\n\n";
        
        // Systemd service for verification
        $content .= "cat > /etc/systemd/system/verify-network.service << 'EOF'\n";
        $content .= "[Unit]\n";
        $content .= "Description=Network Verification\n";
        $content .= "After=NetworkManager.service\n";
        $content .= "Wants=network-online.target\n\n";
        $content .= "[Service]\n";
        $content .= "Type=oneshot\n";
        $content .= "ExecStart=/bin/sleep 10\n";
        $content .= "ExecStart=/usr/local/bin/verify-network.sh\n";
        $content .= "RemainAfterExit=yes\n\n";
        $content .= "[Install]\n";
        $content .= "WantedBy=multi-user.target\n";
        $content .= "EOF\n\n";
        
        $content .= "systemctl enable verify-network.service\n";
        $content .= "echo \"Network setup complete\" >> /var/log/network-setup.log\n\n";
    }
    
    // Cleanup callback
    if (!empty($cleanup_info)) {
        $content .= "# Cleanup script\n";
        $content .= "cat > /usr/local/bin/cleanup-script.sh << 'EOF'\n";
        $content .= "#!/bin/bash\n";
        $content .= "JSON_DATA='{\"server_id\":\"" . ($cleanup_info['server_id'] ?? '') . "\",\"hostname\":\"" . ($cleanup_info['hostname'] ?? '') . "\",\"session_id\":\"" . ($cleanup_info['session_id'] ?? '') . "\",\"servername\":\"" . ($cleanup_info['servername'] ?? '') . "\"}'\n\n";
        $content .= "wget -q -T 30 -t 3 --no-check-certificate --post-data=\"\$JSON_DATA\" -O /dev/null http://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup\n";
        $content .= "EOF\n\n";
        $content .= "chmod +x /usr/local/bin/cleanup-script.sh\n\n";
        
        $content .= "cat > /etc/systemd/system/post-install-cleanup.service << 'EOF'\n";
        $content .= "[Unit]\n";
        $content .= "Description=Post-install Cleanup\n";
        $content .= "After=network-online.target\n\n";
        $content .= "[Service]\n";
        $content .= "Type=oneshot\n";
        $content .= "ExecStart=/usr/local/bin/cleanup-script.sh\n";
        $content .= "ExecStartPost=/bin/systemctl disable post-install-cleanup.service\n\n";
        $content .= "[Install]\n";
        $content .= "WantedBy=multi-user.target\n";
        $content .= "EOF\n\n";
        $content .= "systemctl enable post-install-cleanup.service\n";
    }
    
    $content .= "%end\n";
    return $content;
}

    /**
     * Generate VMware ESXi kickstart file for unattended installation
     */
    private function generateESXiKickstart($network_config, $plain_password = null, $cleanup_info = [], $server_data = []) {
        $hostname = $network_config['hostname'];
        $password = $plain_password ?? $this->generateRandomPassword();
        $server_id = $cleanup_info['server_id'] ?? null;
        
        // Store password in database if server_id is available
        if ($server_id) {
            $this->storeServerPassword($server_id, $password);
        }
        
        // Get all usable IP addresses
        $all_usable_ips = $this->getAllUsableIPs($server_data);
        
        // Calculate subnet mask in CIDR notation
        $cidr = $this->subnetMaskToCIDR($network_config['subnet_mask']);
        
        $content = "# VMware ESXi Kickstart Configuration\n";
        $content .= "# Auto-generated for server: $hostname\n\n";
        
        $content .= "# Accept the VMware End User License Agreement\n";
        $content .= "vmaccepteula\n\n";
        
        $content .= "# Set the root password\n";
        $content .= "rootpw $password\n\n";
        
        $content .= "# Install on the first disk\n";
        $content .= "install --firstdisk --overwritevmfs --ignoreprereqwarnings --ignoreprereqerrors --forceunsupportedinstall\n\n";
        
        // Network configuration - ESXi format
        $content .= "# Network configuration\n";
        $content .= "network --bootproto=static --device=vmnic0 --ip={$network_config['ip_address']} --netmask={$network_config['subnet_mask']} --gateway={$network_config['gateway']} --nameserver={$network_config['dns_primary']} --hostname=$hostname\n\n";
        
        $content .= "# Reboot after installation\n";
        $content .= "reboot\n\n";
        
        $content .= "# First boot script to configure additional settings\n";
        $content .= "%firstboot --interpreter=busybox\n\n";
        
        $content .= "# Enable SSH and ESXi Shell\n";
        $content .= "vim-cmd hostsvc/enable_ssh\n";
        $content .= "vim-cmd hostsvc/start_ssh\n";
        $content .= "vim-cmd hostsvc/enable_esx_shell\n";
        $content .= "vim-cmd hostsvc/start_esx_shell\n\n";
        
        $content .= "# Configure advanced settings\n";
        $content .= "esxcli system settings advanced set -o /UserVars/SuppressShellWarning -i 1\n";
        $content .= "esxcli system settings advanced set -o /UserVars/ESXiShellTimeOut -i 0\n\n";
        
        // Configure additional IP addresses if available
        if (!empty($all_usable_ips) && count($all_usable_ips) > 1) {
            $content .= "# Configure additional IP addresses\n";
            $interface_index = 1;
            foreach ($all_usable_ips as $ip_cidr) {
                list($ip, $cidr_suffix) = explode('/', $ip_cidr);
                // Skip the main IP as it's already configured
                if ($ip !== $network_config['ip_address']) {
                    $content .= "esxcli network ip interface ipv4 address add --interface-name=vmk0 --ipv4=$ip --netmask={$network_config['subnet_mask']} --type=static || true\n";
                    $interface_index++;
                }
            }
            $content .= "\n";
        }
        
        // Set hostname properly
        $content .= "# Set hostname\n";
        $content .= "esxcli system hostname set --fqdn=$hostname\n\n";
        
        // Configure NTP
        $content .= "# Configure NTP\n";
        $content .= "esxcli system ntp set --server=pool.ntp.org\n";
        $content .= "esxcli system ntp set --enabled=true\n\n";
        
        // Configure firewall to allow SSH
        $content .= "# Configure firewall\n";
        $content .= "esxcli network firewall ruleset set --ruleset-id sshServer --enabled true\n";
        $content .= "esxcli network firewall ruleset set --ruleset-id httpClient --enabled true\n\n";
        
        // Cleanup callback (similar to other OS installations)
        // Cleanup callback (similar to other OS installations)
        if (!empty($cleanup_info)) {
            $server_id = $cleanup_info['server_id'] ?? '';
            $hostname_clean = $cleanup_info['hostname'] ?? '';
            $session_id = $cleanup_info['session_id'] ?? '';
            $servername = $cleanup_info['servername'] ?? '';
            
            // Prepare JSON payload
            $json_payload = json_encode([
                'server_id' => $server_id,
                'hostname' => $hostname_clean,
                'session_id' => $session_id,
                'servername' => $servername
            ]);
            $content_length = strlen($json_payload);
            
            $content .= "# Post-installation cleanup\n";
            
            // Primary method: Use printf + openssl s_client for HTTPS (most reliable on ESXi)
            $content .= "cat > /tmp/cleanup_https.sh << 'EOF'\n";
            $content .= "#!/bin/sh\n";
            $content .= "# Wait for network to be fully ready\n";
            $content .= "sleep 15\n";
            $content .= "\n";
            $content .= "# Prepare HTTP request payload\n";
            $content .= "JSON_PAYLOAD='$json_payload'\n";
            $content .= "CONTENT_LENGTH=$content_length\n";
            $content .= "\n";
            $content .= "# Send HTTPS POST request using openssl s_client\n";
            $content .= "printf \"POST /pxe_api_integration.php?action=cleanup HTTP/1.1\\r\\nHost: test.x-zoneit.ro\\r\\nContent-Type: application/json\\r\\nContent-Length: \$CONTENT_LENGTH\\r\\nConnection: close\\r\\n\\r\\n\$JSON_PAYLOAD\" | openssl s_client -connect test.x-zoneit.ro:443 -servername test.x-zoneit.ro -quiet > /tmp/cleanup_https.log 2>&1\n";
            $content .= "\n";
            $content .= "if [ \$? -eq 0 ]; then\n";
            $content .= "    echo \"HTTPS cleanup notification sent successfully\" >> /tmp/cleanup_https.log\n";
            $content .= "else\n";
            $content .= "    echo \"HTTPS cleanup failed, trying HTTP fallback\" >> /tmp/cleanup_https.log\n";
            $content .= "    # HTTP fallback using netcat if available\n";
            $content .= "    if command -v nc >/dev/null 2>&1; then\n";
            $content .= "        printf \"POST /pxe_api_integration.php?action=cleanup HTTP/1.1\\r\\nHost: test.x-zoneit.ro\\r\\nContent-Type: application/json\\r\\nContent-Length: \$CONTENT_LENGTH\\r\\nConnection: close\\r\\n\\r\\n\$JSON_PAYLOAD\" | nc test.x-zoneit.ro 80 >> /tmp/cleanup_https.log 2>&1\n";
            $content .= "    fi\n";
            $content .= "fi\n";
            $content .= "EOF\n\n";
            
            $content .= "chmod +x /tmp/cleanup_https.sh\n";
            $content .= "/tmp/cleanup_https.sh &\n\n";
            
            // Backup method: Use Python for HTTP request since ESXi has limited wget and no curl
            $content .= "cat > /tmp/cleanup_python.py << 'EOF'\n";
            $content .= "#!/usr/bin/python\n";
            $content .= "import urllib2\n";
            $content .= "import urllib\n";
            $content .= "import json\n";
            $content .= "import time\n";
            $content .= "import sys\n";
            $content .= "\n";
            $content .= "# Wait a bit for network to stabilize\n";
            $content .= "time.sleep(20)\n";
            $content .= "\n";
            $content .= "# Prepare data\n";
            $content .= "data = {\n";
            $content .= "    'server_id': '$server_id',\n";
            $content .= "    'hostname': '$hostname_clean',\n";
            $content .= "    'session_id': '$session_id',\n";
            $content .= "    'servername': '$servername'\n";
            $content .= "}\n";
            $content .= "\n";
            $content .= "# Try HTTPS first, then HTTP fallback\n";
            $content .= "for protocol, port in [('https', '443'), ('http', '80')]:\n";
            $content .= "    try:\n";
            $content .= "        url = '%s://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup' % protocol\n";
            $content .= "        req = urllib2.Request(url, data=json.dumps(data), headers={'Content-Type': 'application/json'})\n";
            $content .= "        response = urllib2.urlopen(req, timeout=30)\n";
            $content .= "        print('Cleanup notification sent via %s' % protocol.upper())\n";
            $content .= "        sys.exit(0)\n";
            $content .= "    except Exception as e:\n";
            $content .= "        print('%s failed: %s' % (protocol.upper(), str(e)))\n";
            $content .= "        continue\n";
            $content .= "\n";
            $content .= "# Final fallback: GET with query string\n";
            $content .= "try:\n";
            $content .= "    params = urllib.urlencode(data)\n";
            $content .= "    url = 'http://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup&' + params\n";
            $content .= "    response = urllib2.urlopen(url, timeout=30)\n";
            $content .= "    print('Cleanup notification sent via HTTP GET')\n";
            $content .= "except Exception as e:\n";
            $content .= "    print('Final GET fallback failed: %s' % str(e))\n";
            $content .= "    pass\n";
            $content .= "EOF\n\n";
            
            $content .= "chmod +x /tmp/cleanup_python.py\n";
            
            // Schedule both methods with delays
            $content .= "# Schedule primary cleanup attempt in 1 minute via cron\n";
            $content .= "echo '* * * * * /tmp/cleanup_https.sh > /tmp/cleanup-cron1.log 2>&1' >> /var/spool/cron/crontabs/root\n";
            $content .= "# Schedule backup cleanup attempt in 3 minutes via cron\n";
            $content .= "echo '*/3 * * * * /tmp/cleanup_python.py > /tmp/cleanup-cron2.log 2>&1' >> /var/spool/cron/crontabs/root\n";
            $content .= "kill -HUP \\$(cat /var/run/crond.pid) 2>/dev/null || true\n\n";
        }
        
        $content .= "# End of firstboot script\n";
        $content .= "%end\n";
        
        return $content;
    }
    
    /**
     * Generate comprehensive Debian preseed file (similar to Ubuntu 18.04 format).
     */
    private function generatePreseed($network_config, $plain_password = null, $cleanup_info = [], $server_data = []) {
        $hostname = $network_config['hostname'];
        $password = $plain_password ?? $this->generateRandomPassword();
        
        // Generate MD5 hash for the password (consistent with Ubuntu 18.04 format)
        $password_hash = crypt($password, '$1$' . substr(md5(rand()), 0, 8) . '$');
        
        // Extract MAC address for interface configuration
        $mac_address = $network_config['mac'] ?? '';
        
        // Calculate all usable IP addresses from main_ip and additional_ips
        $all_usable_ips = $this->getAllUsableIPs($server_data);
        
        $content = "### Locales and keyboard\n";
        $content .= "d-i debian-installer/locale string en_US\n";
        $content .= "d-i debian-installer/language string en\n";
        $content .= "d-i debian-installer/country string US\n";
        $content .= "d-i localechooser/supported-locales multiselect en_US.UTF-8\n";
        $content .= "d-i console-setup/ask_detect boolean false\n";
        $content .= "d-i keyboard-configuration/xkb-keymap select us\n";
        $content .= "d-i keyboard-configuration/layoutcode string us\n";
        $content .= "d-i keyboard-configuration/variant select USA\n";
        $content .= "d-i keyboard-configuration/model select Generic 105-key PC\n";
        $content .= "d-i console-keymaps-at/keymap select us\n";
        $content .= "d-i console-setup/variant select USA\n\n";
        
        $content .= "### Network configuration\n";
        $content .= "d-i netcfg/choose_interface select auto\n";
        $content .= "d-i netcfg/disable_autoconfig boolean true\n";
        $content .= "d-i netcfg/get_ipaddress string {$network_config['ip_address']}\n";
        $content .= "d-i netcfg/get_netmask string {$network_config['subnet_mask']}\n";
        $content .= "d-i netcfg/get_gateway string {$network_config['gateway']}\n";
        $content .= "d-i netcfg/get_nameservers string {$network_config['dns_primary']}\n";
        $content .= "d-i netcfg/confirm_static boolean true\n";
        $content .= "d-i netcfg/get_hostname string $hostname\n";
        $content .= "d-i netcfg/get_domain string local\n\n";
        
        $content .= "### Mirror settings\n";
        $content .= "d-i mirror/country string manual\n";
        $content .= "d-i mirror/http/hostname string deb.debian.org\n";
        $content .= "d-i mirror/http/directory string /debian\n";
        $content .= "d-i mirror/http/proxy string\n\n";
        
        $content .= "### Time and clock setup\n";
        $content .= "d-i clock-setup/utc boolean true\n";
        $content .= "d-i time/zone string Europe/Bucharest\n";
        $content .= "d-i clock-setup/ntp boolean true\n\n";
        
        $content .= "### User and password setup\n";
        $content .= "d-i passwd/root-login boolean true\n";
        $content .= "d-i passwd/root-password-crypted password $password_hash\n";
        $content .= "d-i passwd/user-fullname string Default User\n";
        $content .= "d-i passwd/username string user\n";
        $content .= "d-i passwd/user-password-crypted password $password_hash\n\n";
        
        $content .= "### Disk partitioning (auto, erase entire disk)\n";
        $content .= "d-i partman-auto/method string regular\n";
        $content .= "d-i partman-auto/choose_recipe select atomic\n";
        $content .= "d-i partman-lvm/device_remove_lvm boolean true\n";
        $content .= "d-i partman-md/device_remove_md boolean true\n";
        $content .= "d-i partman-partitioning/confirm_write_new_label boolean true\n";
        $content .= "d-i partman/choose_partition select finish\n";
        $content .= "d-i partman/confirm boolean true\n";
        $content .= "d-i partman/confirm_nooverwrite boolean true\n\n";
        
        $content .= "### Package selection\n";
        $content .= "tasksel tasksel/first multiselect standard\n";
        $content .= "d-i pkgsel/include string openssh-server net-tools curl wget\n";
        $content .= "d-i pkgsel/upgrade select none\n";
        $content .= "d-i pkgsel/update-policy select none\n\n";
        
        $content .= "### Bootloader installation\n";
        $content .= "d-i grub-installer/only_debian boolean true\n";
        $content .= "d-i grub-installer/with_other_os boolean true\n\n";
        
        $content .= "### Finishing the installation\n";
        $content .= "d-i finish-install/reboot_in_progress note\n\n";
        
        $content .= "### Late commands (executed near end of install)\n";
        $content .= "d-i preseed/late_command string \\\n";
        $content .= "    in-target sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet\"/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet net.ifnames=0 biosdevname=0\"/' /etc/default/grub; \\\n";
        $content .= "    in-target update-grub; \\\n";
        $content .= "    in-target sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config; \\\n";
        $content .= "    in-target systemctl enable ssh; \\\n";
        $content .= "    in-target systemctl restart ssh; \\\n";
        $content .= "    echo 'auto lo' > /target/etc/network/interfaces; \\\n";
        $content .= "    echo 'iface lo inet loopback' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo 'auto eth0' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo 'iface eth0 inet static' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    address {$network_config['ip_address']}' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    netmask {$network_config['subnet_mask']}' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    gateway {$network_config['gateway']}' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    dns-nameservers {$network_config['dns_primary']}' >> /target/etc/network/interfaces; \\\n";
        
        // Check if MAC address is available and add hwaddress for interface binding
        if (!empty($mac_address)) {
            $content .= "    echo '    hwaddress ether $mac_address' >> /target/etc/network/interfaces; \\\n";
        }
        
        // Add all additional usable IPs as post-up commands
        if (!empty($all_usable_ips)) {
            foreach ($all_usable_ips as $ip_with_cidr) {
                // Skip the main IP as it's already configured above
                list($ip, $cidr) = explode('/', $ip_with_cidr);
                if ($ip !== $network_config['ip_address']) {
                    $content .= "    echo '    post-up ip addr add $ip_with_cidr dev eth0' >> /target/etc/network/interfaces; \\\n";
                    $content .= "    echo '    pre-down ip addr del $ip_with_cidr dev eth0' >> /target/etc/network/interfaces; \\\n";
                }
            }
        }
        
        $content .= "    in-target systemctl restart networking || true; \\\n";
        
        // -------------------------------------------------------
        // Setup cleanup to run after first boot when network is up
        // -------------------------------------------------------
        if (!empty($cleanup_info)) {
            // Convert all values to strings to match API expectations
            $cleanup_payload = json_encode([
                'server_id'  => (string)($cleanup_info['server_id'] ?? ''),
                'hostname'   => (string)($cleanup_info['hostname'] ?? ''),
                'session_id' => (string)($cleanup_info['session_id'] ?? ''),
                'servername' => (string)($cleanup_info['servername'] ?? '')
            ]);
    
            // Escape for inclusion in single-quoted string inside shell script
            $escaped_payload = str_replace("'", "'\\''", $cleanup_payload);
    
            // Extract individual values for proper JSON construction
            $server_id = $cleanup_info['server_id'] ?? '';
            $hostname = $cleanup_info['hostname'] ?? '';
            $session_id = $cleanup_info['session_id'] ?? '';
            $servername = $cleanup_info['servername'] ?? '';
            
            // Create cleanup script with a simple approach - construct JSON separately
            $content .= "    echo '#!/bin/bash' > /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    echo 'JSON_DATA=\"{\\\"server_id\\\":\\\"$server_id\\\",\\\"hostname\\\":\\\"$hostname\\\",\\\"session_id\\\":\\\"$session_id\\\",\\\"servername\\\":\\\"$servername\\\"}\"' >> /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    echo 'wget -q -T 30 -t 3 --no-check-certificate --post-data=\"\$JSON_DATA\" -O /dev/null http://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup' >> /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    chmod +x /target/usr/local/bin/cleanup-script.sh; \\\n";
            
            // Create systemd service
            $content .= "    echo '[Unit]' > /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Description=Post-installation cleanup' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'After=network-online.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Wants=network-online.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '[Service]' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Type=oneshot' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'ExecStart=/usr/local/bin/cleanup-script.sh' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'ExecStartPost=/bin/systemctl disable post-install-cleanup.service' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '[Install]' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'WantedBy=multi-user.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    in-target systemctl enable post-install-cleanup.service; \\\n";
            $content .= "    echo 'Cleanup service configured'\n";
        } else {
            $content .= "    echo 'No cleanup info provided'\n";
        }
        
        return $content;
    }
    
    /**
     * Generate Ubuntu 18.04 preseed configuration file
     */
    private function generateUbuntu1804Preseed($network_config, $custom_password = null, $cleanup_info = [], $server_data = []) {
        $hostname = $network_config['hostname'];
        $password = $custom_password ?? $this->generateRandomPassword();
        
        // Calculate CIDR for netplan configuration
        $cidr = $this->subnetMaskToCIDR($network_config['subnet_mask']);
        
        // Generate MD5 hash for the password (Ubuntu 18.04 preseed uses MD5)
        $password_hash = crypt($password, '$1$' . substr(md5(rand()), 0, 8) . '$');
        
        // Calculate all usable IP addresses from main_ip and additional_ips
        $all_usable_ips = $this->getAllUsableIPs($server_data);
        
        // Store password in database if server_id is available
        // Note: This would need server_id to be passed, but for now we'll just generate the preseed
        
        $content = "### Locales and keyboard\n";
        $content .= "d-i debian-installer/locale string en_US\n";
        $content .= "d-i debian-installer/language string en\n";
        $content .= "d-i debian-installer/country string US\n";
        $content .= "d-i localechooser/supported-locales multiselect en_US.UTF-8\n";
        $content .= "d-i console-setup/ask_detect boolean false\n";
        $content .= "d-i keyboard-configuration/xkb-keymap select us\n";
        $content .= "d-i keyboard-configuration/layoutcode string us\n";
        $content .= "d-i keyboard-configuration/variant select USA\n";
        $content .= "d-i keyboard-configuration/model select Generic 105-key PC\n";
        $content .= "d-i console-keymaps-at/keymap select us\n";
        $content .= "d-i console-setup/variant select USA\n\n";
        
        $content .= "### Network configuration\n";
        $content .= "d-i netcfg/choose_interface select auto\n";
        $content .= "d-i netcfg/disable_autoconfig boolean true\n";
        $content .= "d-i netcfg/get_ipaddress string {$network_config['ip_address']}\n";
        $content .= "d-i netcfg/get_netmask string {$network_config['subnet_mask']}\n";
        $content .= "d-i netcfg/get_gateway string {$network_config['gateway']}\n";
        $content .= "d-i netcfg/get_nameservers string {$network_config['dns_primary']}\n";
        $content .= "d-i netcfg/confirm_static boolean true\n";
        $content .= "d-i netcfg/get_hostname string $hostname\n";
        $content .= "d-i netcfg/get_domain string local\n\n";
        
        $content .= "### Mirror settings\n";
        $content .= "d-i mirror/country string manual\n";
        $content .= "d-i mirror/http/hostname string archive.ubuntu.com\n";
        $content .= "d-i mirror/http/directory string /ubuntu\n";
        $content .= "d-i mirror/http/proxy string\n\n";
        
        $content .= "### Time and clock setup\n";
        $content .= "d-i clock-setup/utc boolean true\n";
        $content .= "d-i time/zone string Europe/Bucharest\n";
        $content .= "d-i clock-setup/ntp boolean true\n\n";
        
        $content .= "### User and password setup\n";
        $content .= "d-i passwd/root-login boolean true\n";
        $content .= "d-i passwd/root-password-crypted password $password_hash\n";
        $content .= "d-i passwd/user-fullname string Default User\n";
        $content .= "d-i passwd/username string user\n";
        $content .= "d-i passwd/user-password-crypted password $password_hash\n\n";
        
        $content .= "### Disk partitioning (auto, erase entire disk)\n";
        $content .= "d-i partman-auto/method string regular\n";
        $content .= "d-i partman-auto/choose_recipe select atomic\n";
        $content .= "d-i partman-lvm/device_remove_lvm boolean true\n";
        $content .= "d-i partman-md/device_remove_md boolean true\n";
        $content .= "d-i partman-partitioning/confirm_write_new_label boolean true\n";
        $content .= "d-i partman/choose_partition select finish\n";
        $content .= "d-i partman/confirm boolean true\n";
        $content .= "d-i partman/confirm_nooverwrite boolean true\n\n";
        
        $content .= "### Package selection\n";
        $content .= "tasksel tasksel/first multiselect standard\n";
        $content .= "d-i pkgsel/include string openssh-server net-tools curl wget\n";
        $content .= "d-i pkgsel/upgrade select none\n";
        $content .= "d-i pkgsel/update-policy select none\n\n";
        
        $content .= "### Bootloader installation\n";
        $content .= "d-i grub-installer/only_debian boolean true\n";
        $content .= "d-i grub-installer/with_other_os boolean true\n\n";
        
        $content .= "### Finishing the installation\n";
        $content .= "d-i finish-install/reboot_in_progress note\n\n";
        
        $content .= "### Late commands (executed near end of install)\n";
        $content .= "d-i preseed/late_command string \\\n";
        $content .= "    in-target sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet splash\"/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet splash net.ifnames=0 biosdevname=0\"/' /etc/default/grub; \\\n";
        $content .= "    in-target update-grub; \\\n";
        $content .= "    in-target sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config; \\\n";
        $content .= "    in-target systemctl enable ssh; \\\n";
        $content .= "    in-target systemctl restart ssh; \\\n";
        $content .= "    echo 'network:' > /target/etc/netplan/01-netcfg.yaml; \\\n";
        $content .= "    echo '  version: 2' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        $mac_address = $network_config['mac'] ?? '';
        
        $content .= "    echo '  ethernets:' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        $content .= "    echo '    eth0:' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        
        // Add MAC address matching if available
        if (!empty($mac_address)) {
            $content .= "    echo '      match:' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
            $content .= "    echo '        macaddress: $mac_address' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        }
        
        // Build addresses array for netplan - include all usable IPs
        $addresses_array = [];
        if (!empty($all_usable_ips)) {
            $addresses_array = $all_usable_ips;
        } else {
            // Fallback to original single IP if no usable IPs found
            $addresses_array[] = "{$network_config['ip_address']}/$cidr";
        }
        
        $addresses_str = implode(', ', $addresses_array);
        
        $content .= "    echo '      addresses: [$addresses_str]' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        $content .= "    echo '      gateway4: {$network_config['gateway']}' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        $content .= "    echo '      nameservers:' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        $content .= "    echo '        addresses: [{$network_config['dns_primary']}]' >> /target/etc/netplan/01-netcfg.yaml; \\\n";
        $content .= "    in-target netplan apply || true; \\\n";
        
        // -------------------------------------------------------
        // Setup cleanup to run after first boot when network is up
        // -------------------------------------------------------
        if (!empty($cleanup_info)) {
            // Extract individual values for proper JSON construction
            $server_id = $cleanup_info['server_id'] ?? '';
            $hostname = $cleanup_info['hostname'] ?? '';
            $session_id = $cleanup_info['session_id'] ?? '';
            $servername = $cleanup_info['servername'] ?? '';
            
            // Create cleanup script with proper JSON formatting
            $content .= "    echo '#!/bin/bash' > /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    echo 'JSON_DATA=\"{\\\"server_id\\\":\\\"$server_id\\\",\\\"hostname\\\":\\\"$hostname\\\",\\\"session_id\\\":\\\"$session_id\\\",\\\"servername\\\":\\\"$servername\\\"}\"' >> /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    echo 'wget -q -T 30 -t 3 --no-check-certificate --post-data=\"\$JSON_DATA\" -O /dev/null http://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup' >> /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    chmod +x /target/usr/local/bin/cleanup-script.sh; \\\n";
            
            // Create systemd service
            $content .= "    echo '[Unit]' > /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Description=Post-installation cleanup' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'After=network-online.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Wants=network-online.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '[Service]' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Type=oneshot' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'ExecStart=/usr/local/bin/cleanup-script.sh' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'ExecStartPost=/bin/systemctl disable post-install-cleanup.service' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '[Install]' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'WantedBy=multi-user.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    in-target systemctl enable post-install-cleanup.service; \\\n";
            $content .= "    echo 'Cleanup service configured'\n";
        } else {
            $content .= "    echo 'No cleanup info provided'\n";
        }
        
        return $content;
    }
    
    /**
     * Generate Proxmox VE preseed configuration file
     */
    private function generateProxmoxPreseed($network_config, $custom_password = null, $cleanup_info = [], $server_data = []) {
        $hostname = $network_config['hostname'];
        $password = $custom_password ?? $this->generateRandomPassword();
        
        // Calculate CIDR for network configuration
        $cidr = $this->subnetMaskToCIDR($network_config['subnet_mask']);
        
        // Generate MD5 hash for the password (Proxmox preseed uses MD5)
        $password_hash = crypt($password, '$1$' . substr(md5(rand()), 0, 8) . '$');
        
        // Calculate all usable IP addresses from main_ip and additional_ips
        $all_usable_ips = $this->getAllUsableIPs($server_data);
        
        // Store password in database if server_id is available
        $server_id = $cleanup_info['server_id'] ?? null;
        if ($server_id) {
            $this->storeServerPassword($server_id, $password);
        }
        
        $content = "### Locales and keyboard\n";
        $content .= "d-i debian-installer/locale string en_US\n";
        $content .= "d-i debian-installer/language string en\n";
        $content .= "d-i debian-installer/country string US\n";
        $content .= "d-i localechooser/supported-locales multiselect en_US.UTF-8\n";
        $content .= "d-i console-setup/ask_detect boolean false\n";
        $content .= "d-i keyboard-configuration/xkb-keymap select us\n";
        $content .= "d-i keyboard-configuration/layoutcode string us\n";
        $content .= "d-i keyboard-configuration/variant select USA\n";
        $content .= "d-i keyboard-configuration/model select Generic 105-key PC\n";
        $content .= "d-i console-keymaps-at/keymap select us\n";
        $content .= "d-i console-setup/variant select USA\n\n";
        
        $content .= "### Network configuration\n";
        $content .= "d-i netcfg/choose_interface select auto\n";
        $content .= "d-i netcfg/disable_autoconfig boolean true\n";
        $content .= "d-i netcfg/get_ipaddress string {$network_config['ip_address']}\n";
        $content .= "d-i netcfg/get_netmask string {$network_config['subnet_mask']}\n";
        $content .= "d-i netcfg/get_gateway string {$network_config['gateway']}\n";
        $content .= "d-i netcfg/get_nameservers string {$network_config['dns_primary']}\n";
        $content .= "d-i netcfg/confirm_static boolean true\n";
        $content .= "d-i netcfg/get_hostname string $hostname\n";
        $content .= "d-i netcfg/get_domain string local\n\n";
        
        $content .= "### Mirror settings (use Proxmox repository)\n";
        $content .= "d-i mirror/country string manual\n";
        $content .= "d-i mirror/http/hostname string download.proxmox.com\n";
        $content .= "d-i mirror/http/directory string /debian\n";
        $content .= "d-i mirror/http/proxy string\n\n";
        
        $content .= "### Time and clock setup\n";
        $content .= "d-i clock-setup/utc boolean true\n";
        $content .= "d-i time/zone string Europe/Bucharest\n";
        $content .= "d-i clock-setup/ntp boolean true\n\n";
        
        $content .= "### User and password setup\n";
        $content .= "d-i passwd/root-login boolean true\n";
        $content .= "d-i passwd/root-password-crypted password $password_hash\n";
        $content .= "d-i passwd/user-fullname string Proxmox Administrator\n";
        $content .= "d-i passwd/username string admin\n";
        $content .= "d-i passwd/user-password-crypted password $password_hash\n";
        $content .= "d-i user-setup/allow-password-weak boolean true\n\n";
        
        $content .= "### Disk partitioning (ZFS root with single disk)\n";
        $content .= "d-i partman-auto/method string zfs\n";
        $content .= "d-i partman-auto-zfs/disk string /dev/sda\n";
        $content .= "d-i partman-auto-zfs/pool_name string rpool\n";
        $content .= "d-i partman-auto-zfs/root_pool_mountpoint string /\n";
        $content .= "d-i partman-lvm/device_remove_lvm boolean true\n";
        $content .= "d-i partman-md/device_remove_md boolean true\n";
        $content .= "d-i partman-partitioning/confirm_write_new_label boolean true\n";
        $content .= "d-i partman/choose_partition select finish\n";
        $content .= "d-i partman/confirm boolean true\n";
        $content .= "d-i partman/confirm_nooverwrite boolean true\n\n";
        
        $content .= "### Package selection (Proxmox VE)\n";
        $content .= "tasksel tasksel/first multiselect standard\n";
        $content .= "d-i pkgsel/include string proxmox-ve openssh-server net-tools curl wget bridge-utils\n";
        $content .= "d-i pkgsel/upgrade select none\n";
        $content .= "d-i pkgsel/update-policy select none\n\n";
        
        $content .= "### Proxmox specific configuration\n";
        $content .= "d-i preseed/early_command string \\\n";
        $content .= "    wget -O /tmp/proxmox-release.gpg http://download.proxmox.com/debian/proxmox-release-bullseye.gpg; \\\n";
        $content .= "    cp /tmp/proxmox-release.gpg /target/etc/apt/trusted.gpg.d/; \\\n";
        $content .= "    echo 'deb [arch=amd64] http://download.proxmox.com/debian/pve bullseye pve-no-subscription' >> /target/etc/apt/sources.list\n\n";
        
        $content .= "### Bootloader installation\n";
        $content .= "d-i grub-installer/only_debian boolean true\n";
        $content .= "d-i grub-installer/with_other_os boolean true\n\n";
        
        $content .= "### Finishing the installation\n";
        $content .= "d-i finish-install/reboot_in_progress note\n\n";
        
        $content .= "### Late commands (executed near end of install)\n";
        $content .= "d-i preseed/late_command string \\\n";
        $content .= "    in-target sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet\"/GRUB_CMDLINE_LINUX_DEFAULT=\"quiet intel_iommu=on iommu=pt\"/' /etc/default/grub; \\\n";
        $content .= "    in-target update-grub; \\\n";
        $content .= "    in-target sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config; \\\n";
        $content .= "    in-target systemctl enable ssh; \\\n";
        $content .= "    in-target systemctl restart ssh; \\\n";
        $content .= "    echo 'auto lo' > /target/etc/network/interfaces; \\\n";
        $content .= "    echo 'iface lo inet loopback' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo 'auto vmbr0' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo 'iface vmbr0 inet static' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    address {$network_config['ip_address']}/{$cidr}' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    gateway {$network_config['gateway']}' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    bridge-ports eth0' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    bridge-stp off' >> /target/etc/network/interfaces; \\\n";
        $content .= "    echo '    bridge-fd 0' >> /target/etc/network/interfaces; \\\n";
        
        // Check if MAC address is available and add hwaddress for interface binding
        $mac_address = $network_config['mac'] ?? '';
        if (!empty($mac_address)) {
            $content .= "    echo '    hwaddress ether $mac_address' >> /target/etc/network/interfaces; \\\n";
        }
        
        // Add all additional usable IPs as aliases on the bridge
        if (!empty($all_usable_ips)) {
            $alias_index = 1;
            foreach ($all_usable_ips as $ip_with_cidr) {
                // Skip the main IP as it's already configured above
                list($ip, $cidr_suffix) = explode('/', $ip_with_cidr);
                if ($ip !== $network_config['ip_address']) {
                    $content .= "    echo '' >> /target/etc/network/interfaces; \\\n";
                    $content .= "    echo 'auto vmbr0:$alias_index' >> /target/etc/network/interfaces; \\\n";
                    $content .= "    echo 'iface vmbr0:$alias_index inet static' >> /target/etc/network/interfaces; \\\n";
                    $content .= "    echo '    address $ip_with_cidr' >> /target/etc/network/interfaces; \\\n";
                    $alias_index++;
                }
            }
        }
        
        $content .= "    echo 'nameserver {$network_config['dns_primary']}' > /target/etc/resolv.conf; \\\n";
        $content .= "    in-target systemctl restart networking || true; \\\n";
        
        // Configure Proxmox specific settings
        $content .= "    in-target apt-get update; \\\n";
        $content .= "    in-target apt-get install -y proxmox-ve; \\\n";
        $content .= "    in-target systemctl enable pveproxy; \\\n";
        $content .= "    in-target systemctl enable pvedaemon; \\\n";
        $content .= "    in-target systemctl enable pvestatd; \\\n";
        $content .= "    in-target systemctl enable pvescheduler; \\\n";
        $content .= "    in-target systemctl enable pve-cluster; \\\n";
        
        // Configure Proxmox storage and defaults
        $content .= "    echo 'dir: local' > /target/etc/pve/storage.cfg; \\\n";
        $content .= "    echo '    path /var/lib/vz' >> /target/etc/pve/storage.cfg; \\\n";
        $content .= "    echo '    content iso,vztmpl,backup' >> /target/etc/pve/storage.cfg; \\\n";
        $content .= "    echo '    maxfiles 3' >> /target/etc/pve/storage.cfg; \\\n";
        
        // Setup cleanup to run after first boot when network is up
        if (!empty($cleanup_info)) {
            $server_id = $cleanup_info['server_id'] ?? '';
            $hostname_clean = $cleanup_info['hostname'] ?? '';
            $session_id = $cleanup_info['session_id'] ?? '';
            $servername = $cleanup_info['servername'] ?? '';
            
            // Create cleanup script with proper JSON formatting
            $content .= "    echo '#!/bin/bash' > /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    echo 'JSON_DATA=\"{\\\"server_id\\\":\\\"$server_id\\\",\\\"hostname\\\":\\\"$hostname_clean\\\",\\\"session_id\\\":\\\"$session_id\\\",\\\"servername\\\":\\\"$servername\\\"}\"' >> /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    echo 'wget -q -T 30 -t 3 --no-check-certificate --post-data=\"\$JSON_DATA\" -O /dev/null http://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup' >> /target/usr/local/bin/cleanup-script.sh; \\\n";
            $content .= "    chmod +x /target/usr/local/bin/cleanup-script.sh; \\\n";
            
            // Create systemd service
            $content .= "    echo '[Unit]' > /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Description=Post-installation cleanup' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'After=network-online.target pveproxy.service' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Wants=network-online.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '[Service]' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'Type=oneshot' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'ExecStart=/usr/local/bin/cleanup-script.sh' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'ExecStartPost=/bin/systemctl disable post-install-cleanup.service' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo '[Install]' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    echo 'WantedBy=multi-user.target' >> /target/etc/systemd/system/post-install-cleanup.service; \\\n";
            $content .= "    in-target systemctl enable post-install-cleanup.service; \\\n";
            $content .= "    echo 'Cleanup service configured'\n";
        } else {
            $content .= "    echo 'No cleanup info provided'\n";
        }
        
        return $content;
    }
    
    /**
     * Generate Proxmox VE answer file in TOML format for automated installation
     */
    private function generateProxmoxAnswerFile($network_config, $custom_password = null, $cleanup_info = [], $server_data = []) {
        $hostname = $network_config['hostname'];
        $password = $custom_password ?? $this->generateRandomPassword();
        
        // Store password in database if server_id is available
        $server_id = $cleanup_info['server_id'] ?? null;
        if ($server_id) {
            $this->storeServerPassword($server_id, $password);
        }
        
        // Calculate CIDR for network configuration
        $cidr = $this->subnetMaskToCIDR($network_config['subnet_mask']);
        $mac_address = $network_config['mac'] ?? '';
        
        $content = "# Proxmox VE Automated Installation Answer File\n";
        $content .= "# Generated by PXE API Integration\n\n";
        
        $content .= "[global]\n";
        $content .= "keyboard = \"us\"\n";
        $content .= "country = \"us\"\n";
        $content .= "fqdn = \"$hostname\"\n";
        $content .= "mailto = \"admin@$hostname\"\n";
        $content .= "timezone = \"Europe/Bucharest\"\n";
        $content .= "root-password = \"$password\"\n\n";
        
        $content .= "[network]\n";
        $content .= "source = \"from-answer-file\"\n";
        $content .= "use-dhcp = false\n";
        $content .= "cidr = \"{$network_config['ip_address']}/$cidr\"\n";
        $content .= "dns = \"{$network_config['dns_primary']}\"\n";
        $content .= "gateway = \"{$network_config['gateway']}\"\n";
        
        // Add interface specification if MAC address is available
        if (!empty($mac_address)) {
            $content .= "filter = \"DEVPATH=*\"\n";
        }
        $content .= "\n";
        
        $content .= "[disk-setup]\n";
        $content .= "filesystem = \"zfs\"\n";
        $content .= "disk-list = []\n";
        $content .= "filter-match = \".*\"\n";
        $content .= "zfs-raid = \"single\"\n";
        $content .= "zfs-checksum = \"on\"\n";
        $content .= "zfs-compression = \"on\"\n";
        $content .= "zfs-copies = 1\n";
        $content .= "zfs-arc-max = 2048\n\n";
        
        // Add post-installation webhook for cleanup if cleanup info is available
        if (!empty($cleanup_info)) {
            $cleanup_payload = json_encode(array_filter([
                'server_id'  => $cleanup_info['server_id'] ?? null,
                'hostname'   => $cleanup_info['hostname'] ?? null,
                'session_id' => $cleanup_info['session_id'] ?? null,
                'servername' => $cleanup_info['servername'] ?? null
            ]));

            $content .= "[post-installation-webhook]\n";
            $content .= "url = \"https://test.x-zoneit.ro/pxe_api_integration.php?action=cleanup\"\n";
            $content .= "method = \"POST\"\n";
            $content .= "payload = '$cleanup_payload'\n";
            $content .= "headers = [\"Content-Type: application/json\"]\n";
            $content .= "retries = 3\n";
            $content .= "timeout = 30\n\n";
        }
        
        // Add first-boot script for additional configuration
        $content .= "[first-boot]\n";
        $content .= "script = '''#!/bin/bash\n";
        $content .= "# Configure network interface naming\n";
        $content .= "echo 'GRUB_CMDLINE_LINUX_DEFAULT=\"\$GRUB_CMDLINE_LINUX_DEFAULT net.ifnames=0 biosdevname=0\"' >> /etc/default/grub\n";
        $content .= "update-grub\n";
        $content .= "\n";
        $content .= "# Configure SSH for remote access\n";
        $content .= "sed -i 's/#PermitRootLogin.*/PermitRootLogin yes/' /etc/ssh/sshd_config\n";
        $content .= "systemctl restart ssh\n";
        $content .= "\n";
        $content .= "# Configure Proxmox bridge with static IP\n";
        $content .= "cat > /etc/network/interfaces << EOF\n";
        $content .= "auto lo\n";
        $content .= "iface lo inet loopback\n";
        $content .= "\n";
        $content .= "auto eth0\n";
        $content .= "iface eth0 inet manual\n";
        
        if (!empty($mac_address)) {
            $content .= "    hwaddress ether $mac_address\n";
        }
        
        $content .= "\n";
        $content .= "auto vmbr0\n";
        $content .= "iface vmbr0 inet static\n";
        $content .= "    address {$network_config['ip_address']}/$cidr\n";
        $content .= "    gateway {$network_config['gateway']}\n";
        $content .= "    bridge-ports eth0\n";
        $content .= "    bridge-stp off\n";
        $content .= "    bridge-fd 0\n";
        $content .= "EOF\n";
        $content .= "\n";
        $content .= "# Configure DNS\n";
        $content .= "echo \"nameserver {$network_config['dns_primary']}\" > /etc/resolv.conf\n";
        $content .= "\n";
        $content .= "# Restart networking\n";
        $content .= "systemctl restart networking\n";
        $content .= "'''\n\n";

        return $content;
    }

    /**
     * Remove ACLs for a server during PXE reinstall start
     * This function removes existing ACL rules from the network switch
     * to ensure clean network access during the reinstall process
     */
    public function removeServerACLs($server_id, $server_type) {
        try {
            $this->logOperation("Starting ACL removal for server $server_id ($server_type)", [
                'server_id' => $server_id,
                'server_type' => $server_type
            ]);

            // Get server information including switch details
            $server = $this->getServerInfo($server_id, $server_type);
            if (!$server) {
                throw new Exception("Server not found: $server_id");
            }

            // Check if server has switch configuration
            if (empty($server['switch_ip']) || empty($server['port1'])) {
                $this->logOperation("Server has no switch configuration - skipping ACL removal", [
                    'server_id' => $server_id,
                    'switch_ip' => $server['switch_ip'] ?? 'not set',
                    'port' => $server['port1'] ?? 'not set'
                ]);
                return [
                    'success' => true,
                    'message' => 'No switch configuration found - ACL removal skipped',
                    'acl_removed' => false
                ];
            }

            // Get all subnets currently assigned to this server
            $subnets = $this->getServerSubnets($server_id, $server_type);

            if (empty($subnets)) {
                $this->logOperation("No subnets found for server - skipping ACL removal", [
                    'server_id' => $server_id
                ]);
                return [
                    'success' => true,
                    'message' => 'No subnets assigned - ACL removal skipped',
                    'acl_removed' => false
                ];
            }

            // Use existing ACL removal functions from api_admin_subnets.php
            $switchIp = $server['switch_ip'];
            $switchPassword = $server['root_password'];
            $portNumber = $server['port1'];
            $portName = $server['port_name']; // Get the actual port name from inventory_switch_ports

            // Create ACL removal commands directly
            $aclRemovalCommands = $this->createACLRemovalCommands($portNumber, $portName, $subnets);

            $this->logOperation("ACL removal commands generated", [
                'server_id' => $server_id,
                'port' => $portNumber,
                'subnets_count' => count($subnets),
                'commands_count' => count($aclRemovalCommands)
            ]);

            if (empty($aclRemovalCommands)) {
                $this->logOperation("No ACL removal commands generated", [
                    'server_id' => $server_id,
                    'port' => $portNumber
                ]);
                return [
                    'success' => true,
                    'message' => 'No ACL removal commands needed',
                    'acl_removed' => false
                ];
            }

            // Execute ACL removal via SSH
            $result = $this->executeACLRemovalCommands($switchIp, $switchPassword, $aclRemovalCommands);

            if ($result['success']) {
                $this->logOperation("ACL removal completed successfully", [
                    'server_id' => $server_id,
                    'switch_ip' => $switchIp,
                    'port' => $portNumber,
                    'subnets_count' => count($subnets)
                ]);

                return [
                    'success' => true,
                    'message' => 'ACLs removed successfully from switch interface',
                    'acl_removed' => true,
                    'switch_ip' => $switchIp,
                    'port' => $portNumber,
                    'subnets_affected' => count($subnets)
                ];
            } else {
                $this->logOperation("ACL removal failed", [
                    'server_id' => $server_id,
                    'error' => $result['error']
                ]);

                // Don't fail the entire PXE process if ACL removal fails
                return [
                    'success' => true,
                    'message' => 'ACL removal failed but continuing with PXE reinstall',
                    'acl_removed' => false,
                    'error' => $result['error'],
                    'warning' => 'Manual ACL cleanup may be required'
                ];
            }

        } catch (Exception $e) {
            $this->logOperation("Exception during ACL removal: " . $e->getMessage(), [
                'server_id' => $server_id,
                'error' => $e->getMessage()
            ]);

            // Don't fail the entire PXE process if ACL removal fails
            return [
                'success' => true,
                'message' => 'ACL removal encountered an error but continuing with PXE reinstall',
                'acl_removed' => false,
                'error' => $e->getMessage(),
                'warning' => 'Manual ACL cleanup may be required'
            ];
        }
    }

    /**
     * Reapply ACLs for a server after PXE reinstall completion
     * This function restores ACL rules on the network switch
     * to secure the server's network access after reinstall
     */
    public function reapplyServerACLs($server_id, $server_type) {
        try {
            $this->logOperation("Starting ACL reapplication for server $server_id ($server_type)", [
                'server_id' => $server_id,
                'server_type' => $server_type
            ]);

            // Get server information including switch details
            $server = $this->getServerInfo($server_id, $server_type);
            if (!$server) {
                throw new Exception("Server not found: $server_id");
            }

            // Check if server has switch configuration
            if (empty($server['switch_ip']) || empty($server['port1'])) {
                $this->logOperation("Server has no switch configuration - skipping ACL reapplication", [
                    'server_id' => $server_id,
                    'switch_ip' => $server['switch_ip'] ?? 'not set',
                    'port' => $server['port1'] ?? 'not set'
                ]);
                return [
                    'success' => true,
                    'message' => 'No switch configuration found - ACL reapplication skipped',
                    'acl_applied' => false
                ];
            }

            // Get all subnets currently assigned to this server
            $subnets = $this->getServerSubnets($server_id, $server_type);

            if (empty($subnets)) {
                $this->logOperation("No subnets found for server - skipping ACL reapplication", [
                    'server_id' => $server_id
                ]);
                return [
                    'success' => true,
                    'message' => 'No subnets assigned - ACL reapplication skipped',
                    'acl_applied' => false
                ];
            }

            // Use internal ACL creation method
            $switchIp = $server['switch_ip'];
            $switchPassword = $server['root_password'];
            $portNumber = $server['port1'];
            $portName = $server['port_name']; // Get the actual port name from inventory_switch_ports

            // Create ACL commands
            $aclCommands = $this->createACLCommands($portNumber, $portName, $subnets);

            if (empty($aclCommands)) {
                $this->logOperation("No ACL commands generated", [
                    'server_id' => $server_id,
                    'port' => $portNumber
                ]);
                return [
                    'success' => true,
                    'message' => 'No ACL commands needed',
                    'acl_applied' => false
                ];
            }

            // Execute ACL creation via SSH
            $result = $this->executeACLCommands($switchIp, $switchPassword, $aclCommands);

            if ($result['success']) {
                $this->logOperation("ACL reapplication completed successfully", [
                    'server_id' => $server_id,
                    'switch_ip' => $switchIp,
                    'port' => $portNumber,
                    'subnets_count' => count($subnets)
                ]);

                return [
                    'success' => true,
                    'message' => 'ACLs reapplied successfully to switch interface',
                    'acl_applied' => true,
                    'switch_ip' => $switchIp,
                    'port' => $portNumber,
                    'subnets_configured' => count($subnets)
                ];
            } else {
                $this->logOperation("ACL reapplication failed", [
                    'server_id' => $server_id,
                    'error' => $result['error']
                ]);

                return [
                    'success' => false,
                    'message' => 'ACL reapplication failed',
                    'acl_applied' => false,
                    'error' => $result['error'],
                    'warning' => 'Manual ACL configuration may be required'
                ];
            }

        } catch (Exception $e) {
            $this->logOperation("Exception during ACL reapplication: " . $e->getMessage(), [
                'server_id' => $server_id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'ACL reapplication encountered an error',
                'acl_applied' => false,
                'error' => $e->getMessage(),
                'warning' => 'Manual ACL configuration may be required'
            ];
        }
    }

    /**
     * Get server information including switch details and port name
     */
    private function getServerInfo($server_id, $server_type) {
        try {
            $table = ($server_type === 'dedicated') ? 'inventory_dedicated_servers' : 'blade_server_inventory';

            $stmt = $this->pdo->prepare("
                SELECT s.*,
                       sw.switch_ip,
                       sw.root_password,
                       sp.port_name,
                       sp.port_description
                FROM $table s
                LEFT JOIN inventory_switches sw ON s.switch_id = sw.id
                LEFT JOIN inventory_switch_ports sp ON (sw.id = sp.switch_id AND s.port1 = sp.id)
                WHERE s.id = ?
            ");
            $stmt->execute([$server_id]);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                $this->logOperation("Retrieved server info with port details", [
                    'server_id' => $server_id,
                    'server_type' => $server_type,
                    'port1' => $result['port1'] ?? 'not set',
                    'port_name' => $result['port_name'] ?? 'not found',
                    'switch_ip' => $result['switch_ip'] ?? 'not set'
                ]);
            }

            return $result;
        } catch (Exception $e) {
            $this->logOperation("Error getting server info: " . $e->getMessage(), [
                'server_id' => $server_id,
                'server_type' => $server_type
            ]);
            return null;
        }
    }

    /**
     * Get all subnets assigned to a server from main_ip and additional_ips
     */
    private function getServerSubnets($server_id, $server_type) {
        try {
            // Get server information to extract IPs
            $server = $this->getServerInfo($server_id, $server_type);
            if (!$server) {
                return [];
            }

            $subnets = [];

            // Add main_ip if it exists
            if (!empty($server['main_ip'])) {
                $subnets[] = $server['main_ip'];
                $this->logOperation("Found main_ip for server", [
                    'server_id' => $server_id,
                    'main_ip' => $server['main_ip']
                ]);
            }

            // Add additional_ips if they exist
            if (!empty($server['additional_ips'])) {
                $additional_ips = array_filter(array_map('trim', explode(',', $server['additional_ips'])));
                foreach ($additional_ips as $ip) {
                    if (!empty($ip)) {
                        $subnets[] = $ip;
                    }
                }
                $this->logOperation("Found additional_ips for server", [
                    'server_id' => $server_id,
                    'additional_ips' => $additional_ips
                ]);
            }

            $this->logOperation("Retrieved server subnets from main_ip and additional_ips", [
                'server_id' => $server_id,
                'subnets_count' => count($subnets),
                'subnets' => $subnets
            ]);

            return $subnets;
        } catch (Exception $e) {
            $this->logOperation("Error getting server subnets: " . $e->getMessage(), [
                'server_id' => $server_id,
                'server_type' => $server_type
            ]);
            return [];
        }
    }

    /**
     * Execute ACL removal commands via SSH with detailed debugging
     */
    private function executeACLRemovalCommands($switchIp, $password, $commands) {
        try {
            $this->logOperation("=== Starting ACL Removal SSH Connection ===", [
                'switch_ip' => $switchIp,
                'commands_count' => count($commands),
                'commands' => $commands
            ]);

            if (!extension_loaded('ssh2')) {
                $this->logOperation("SSH2 extension not available", []);
                return ['success' => false, 'error' => 'SSH2 extension not available'];
            }

            $this->logOperation("Attempting SSH connection to switch", ['switch_ip' => $switchIp]);
            $connection = @ssh2_connect($switchIp, 22, ['timeout' => 30]);
            if (!$connection) {
                $this->logOperation("Failed to connect to switch", ['switch_ip' => $switchIp]);
                return ['success' => false, 'error' => 'Could not connect to switch'];
            }

            $this->logOperation("SSH connection established, attempting authentication", []);
            if (!@ssh2_auth_password($connection, 'admin', $password)) {
                $this->logOperation("SSH authentication failed", ['username' => 'admin']);
                return ['success' => false, 'error' => 'SSH authentication failed'];
            }

            $this->logOperation("SSH authentication successful, opening shell", []);
            $shell = ssh2_shell($connection, 'vt102');
            if (!$shell) {
                $this->logOperation("Failed to open SSH shell", []);
                return ['success' => false, 'error' => 'Could not open SSH shell'];
            }

            // Set shell to non-blocking to read responses
            stream_set_blocking($shell, false);

            $this->logOperation("Executing ACL removal commands", ['commands_count' => count($commands)]);
            $allOutput = '';

            // Execute commands one by one with response capture
            foreach ($commands as $index => $command) {
                $this->logOperation("Executing command " . ($index + 1), [
                    'command' => $command,
                    'index' => $index + 1,
                    'total' => count($commands)
                ]);

                // Send command
                fwrite($shell, $command . "\n");
                fflush($shell);

                // Wait and capture response
                usleep(1000000); // Wait 1 second for command to execute

                $output = '';
                $attempts = 0;
                while ($attempts < 10) { // Try to read for up to 10 attempts
                    $data = fread($shell, 4096);
                    if ($data !== false && strlen($data) > 0) {
                        $output .= $data;
                    } else {
                        usleep(100000); // Wait 0.1 seconds before next attempt
                    }
                    $attempts++;
                }

                $allOutput .= "Command: $command\nResponse: $output\n---\n";

                $this->logOperation("Command response received", [
                    'command' => $command,
                    'response_length' => strlen($output),
                    'response_preview' => substr($output, 0, 200)
                ]);
            }

            // Final wait for all commands to complete
            sleep(2);

            // Capture any final output
            $finalOutput = '';
            $attempts = 0;
            while ($attempts < 5) {
                $data = fread($shell, 4096);
                if ($data !== false && strlen($data) > 0) {
                    $finalOutput .= $data;
                } else {
                    usleep(200000);
                }
                $attempts++;
            }

            if (!empty($finalOutput)) {
                $allOutput .= "Final output: $finalOutput\n";
            }

            $this->logOperation("=== Complete SSH Session Output ===", [
                'switch_ip' => $switchIp,
                'total_output_length' => strlen($allOutput),
                'full_output' => $allOutput
            ]);

            fclose($shell);
            ssh2_disconnect($connection);

            $this->logOperation("SSH connection closed successfully", []);
            return [
                'success' => true,
                'message' => 'ACL removal commands executed successfully',
                'output' => $allOutput,
                'commands_executed' => count($commands)
            ];

        } catch (Exception $e) {
            $this->logOperation("Exception during ACL removal SSH execution", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Execute ACL creation commands via SSH with detailed debugging
     */
    private function executeACLCommands($switchIp, $password, $commands) {
        try {
            $this->logOperation("=== Starting ACL Creation SSH Connection ===", [
                'switch_ip' => $switchIp,
                'commands_count' => count($commands),
                'commands' => $commands
            ]);

            if (!extension_loaded('ssh2')) {
                $this->logOperation("SSH2 extension not available", []);
                return ['success' => false, 'error' => 'SSH2 extension not available'];
            }

            $this->logOperation("Attempting SSH connection to switch", ['switch_ip' => $switchIp]);
            $connection = @ssh2_connect($switchIp, 22, ['timeout' => 30]);
            if (!$connection) {
                $this->logOperation("Failed to connect to switch", ['switch_ip' => $switchIp]);
                return ['success' => false, 'error' => 'Could not connect to switch'];
            }

            $this->logOperation("SSH connection established, attempting authentication", []);
            if (!@ssh2_auth_password($connection, 'admin', $password)) {
                $this->logOperation("SSH authentication failed", ['username' => 'admin']);
                return ['success' => false, 'error' => 'SSH authentication failed'];
            }

            $this->logOperation("SSH authentication successful, opening shell", []);
            $shell = ssh2_shell($connection, 'vt102');
            if (!$shell) {
                $this->logOperation("Failed to open SSH shell", []);
                return ['success' => false, 'error' => 'Could not open SSH shell'];
            }

            // Set shell to non-blocking to read responses
            stream_set_blocking($shell, false);

            $this->logOperation("Executing ACL creation commands", ['commands_count' => count($commands)]);
            $allOutput = '';

            // Execute commands one by one with response capture
            foreach ($commands as $index => $command) {
                $this->logOperation("Executing command " . ($index + 1), [
                    'command' => $command,
                    'index' => $index + 1,
                    'total' => count($commands)
                ]);

                // Send command
                fwrite($shell, $command . "\n");
                fflush($shell);

                // Wait and capture response
                usleep(1000000); // Wait 1 second for command to execute

                $output = '';
                $attempts = 0;
                while ($attempts < 10) { // Try to read for up to 10 attempts
                    $data = fread($shell, 4096);
                    if ($data !== false && strlen($data) > 0) {
                        $output .= $data;
                    } else {
                        usleep(100000); // Wait 0.1 seconds before next attempt
                    }
                    $attempts++;
                }

                $allOutput .= "Command: $command\nResponse: $output\n---\n";

                $this->logOperation("Command response received", [
                    'command' => $command,
                    'response_length' => strlen($output),
                    'response_preview' => substr($output, 0, 200)
                ]);
            }

            // Final wait for all commands to complete
            sleep(2);

            // Capture any final output
            $finalOutput = '';
            $attempts = 0;
            while ($attempts < 5) {
                $data = fread($shell, 4096);
                if ($data !== false && strlen($data) > 0) {
                    $finalOutput .= $data;
                } else {
                    usleep(200000);
                }
                $attempts++;
            }

            if (!empty($finalOutput)) {
                $allOutput .= "Final output: $finalOutput\n";
            }

            $this->logOperation("=== Complete SSH Session Output ===", [
                'switch_ip' => $switchIp,
                'total_output_length' => strlen($allOutput),
                'full_output' => $allOutput
            ]);

            fclose($shell);
            ssh2_disconnect($connection);

            $this->logOperation("SSH connection closed successfully", []);
            return [
                'success' => true,
                'message' => 'ACL commands executed successfully',
                'output' => $allOutput,
                'commands_executed' => count($commands)
            ];

        } catch (Exception $e) {
            $this->logOperation("Exception during ACL creation SSH execution", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Create ACL removal commands for a switch interface
     */
    private function createACLRemovalCommands($portNumber, $portName, $subnets = null) {
        $commands = [];

        // Use the actual port name from inventory_switch_ports if available
        // Otherwise fall back to constructing from port number
        if (!empty($portName)) {
            $interfaceName = $portName; // e.g., "Ethernet35"
            // Extract the number from the port name for ACL name (e.g., "et35")
            if (preg_match('/(\d+)$/', $portName, $matches)) {
                $aclName = "et" . $matches[1];
            } else {
                $aclName = "et" . $portName; // fallback
            }
        } else {
            // Fallback to old method if port name not found
            $interfaceName = "Ethernet" . $portNumber;
            $aclName = "et" . $portName;
        }

        $this->logOperation("Creating ACL removal commands", [
            'port_number' => $portNumber,
            'port_name' => $portName,
            'interface' => $interfaceName,
            'acl_name' => $aclName,
            'subnets_count' => is_array($subnets) ? count($subnets) : 0
        ]);

        // Add ACL removal commands
        $commands[] = "enable";
        $commands[] = "configure terminal";
        $commands[] = "interface ethernet $interfaceName";
        $commands[] = "no ip access-group $aclName in";
        $commands[] = "description test";
        $commands[] = "exit";
        $commands[] = "no ip access-list $aclName";
        $commands[] = "exit";
        $commands[] = "write memory";

        $this->logOperation("Created ACL removal commands", [
            'interface' => $interfaceName,
            'acl_name' => $aclName,
            'commands_count' => count($commands),
            'commands' => $commands
        ]);

        return $commands;
    }

    /**
     * Create ACL creation commands for a switch interface
     */
    private function createACLCommands($portNumber, $portName, $subnets) {
        $commands = [];

        // Normalize subnet list (allow string or array)
        $subnetList = [];
        if (is_array($subnets)) {
            $subnetList = $subnets;
        } elseif (!empty($subnets)) {
            $subnetList[] = $subnets;
        }

        // Filter only valid CIDRs
        $subnetList = array_values(array_filter($subnetList, function ($cidr) {
            return strpos($cidr, '/') !== false;
        }));

        if (empty($subnetList)) {
            $this->logOperation("No valid subnet info provided for ACL creation", [
                'port_number' => $portNumber,
                'port_name' => $portName
            ]);
            return $commands;
        }

        // Use the actual port name from inventory_switch_ports if available
        // Otherwise fall back to constructing from port number
        if (!empty($portName)) {
            $interfaceName = $portName; // e.g., "Ethernet35"
            // Extract the number from the port name for ACL name (e.g., "et35")
            if (preg_match('/(\d+)$/', $portName, $matches)) {
                $aclName = "et" . $matches[1];
            } else {
                $aclName = "et" . $portNumber; // fallback
            }
        } else {
            // Fallback to old method if port name not found
            $interfaceName = "Ethernet" . $portNumber;
            $aclName = "et" . $portNumber;
        }

        $this->logOperation("Creating ACL commands", [
            'port' => $portNumber,
            'interface' => $interfaceName,
            'acl_name' => $aclName,
            'subnets' => $subnetList
        ]);

        // ACL base commands
        $commands[] = "configure terminal";
        $commands[] = "ip access-list $aclName";

        // Add a permit line per subnet with incremental sequence numbers starting at 1
        $seq = 1;
        foreach ($subnetList as $cidr) {
            $commands[] = "$seq permit ip $cidr any";
            $seq++;
        }

        // Final deny rule
        $commands[] = "999 deny ip any any";
        $commands[] = "exit";
        $commands[] = "interface $interfaceName";
        $commands[] = "ip access-group $aclName in";
        $commands[] = "exit";
        $commands[] = "exit";

        $this->logOperation("Created ACL commands", [
            'interface' => $interfaceName,
            'subnets_count' => count($subnetList),
            'commands_count' => count($commands)
        ]);

        return $commands;
    }
}

// API endpoint handling
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        // Determine requested action early so we can decide whether full admin authentication is required.
        $action = $_GET['action'] ?? '';

        // For most API calls we still require an authenticated admin session. However, the "cleanup" action is
        // triggered automatically by the freshly-installed server via cloud-init.  That environment cannot
        // securely supply an admin token, therefore we permit this single action to bypass the admin check.
        if ($action !== 'cleanup') {
            $admin_id = auth_admin();
        } else {
            // Skip admin authentication – the request authenticity will instead be validated via the
            // session information supplied in the payload.
            $admin_id = null;
        }
        ensurePXESchema($pdo);
        $manager = new PXENetworkManager($pdo);
        
        // Handle reinstallable_os endpoint - Use reinstallable_os table for blade/dedicated servers
        if (isset($_GET['f']) && $_GET['f'] === 'reinstallable_os') {
            $stmt = $pdo->prepare("SELECT id, name, version, template_file, description, active FROM reinstallable_os WHERE active = 1 ORDER BY name, version");
            $stmt->execute();
            $operating_systems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Format for frontend compatibility
            $formatted_os = [];
            foreach ($operating_systems as $os) {
                $formatted_os[] = [
                    'id' => $os['id'],
                    'name' => $os['name'] . ' ' . $os['version'],
                    'template_file' => $os['template_file'],
                    'description' => $os['description'] ?? ($os['name'] . ' ' . $os['version'])
                ];
            }
            
            echo json_encode($formatted_os);
            exit;
        }
        
        // Handle get_server_for_reinstall endpoint
        if (isset($_GET['f']) && $_GET['f'] === 'get_server_for_reinstall') {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data || !isset($data['server_id']) || !isset($data['server_type'])) {
                throw new Exception('Server ID and type are required');
            }
            
            $server_id = $data['server_id'];
            $server_type = $data['server_type'];
            
            // Fetch server data from appropriate table
            if ($server_type === 'blade') {
                $stmt = $pdo->prepare("SELECT * FROM blade_server_inventory WHERE id = ?");
            } else {
                $stmt = $pdo->prepare("SELECT * FROM inventory_dedicated_servers WHERE id = ?");
            }
            
            $stmt->execute([$server_id]);
            $server = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$server) {
                throw new Exception('Server not found');
            }
            
            // Validate required fields
            if (!$server['mac']) {
                throw new Exception('Server MAC address is required for PXE reinstall');
            }
            
            if (!$server['main_ip']) {
                throw new Exception('Server main IP address is required for PXE reinstall');
            }
            
            echo json_encode([
                'success' => true,
                'server' => $server
            ]);
            exit;
        }
        
        // Handle start_reinstall_session endpoint
        if (isset($_GET['f']) && $_GET['f'] === 'start_reinstall_session') {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data || !isset($data['server_id']) || !isset($data['server_type']) || !isset($data['os_template'])) {
                throw new Exception('Server ID, type, and OS template are required');
            }
            
            // Get server data first
            $server_id = $data['server_id'];
            $server_type = $data['server_type'];
            
            if ($server_type === 'blade') {
                $stmt = $pdo->prepare("SELECT * FROM blade_server_inventory WHERE id = ?");
            } else {
                $stmt = $pdo->prepare("SELECT * FROM dedicated_server_inventory WHERE id = ?");
            }
            
            $stmt->execute([$server_id]);
            $server = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$server) {
                throw new Exception('Server not found');
            }
            
            // Create reinstall session
            $stmt = $pdo->prepare("
                INSERT INTO pxe_reinstall_sessions 
                (server_id, server_type, server_label, mac_address, ip_address, hostname, os_template, network_config, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            
            // Use reflection to access the private getNetworkConfigFromDatabase method
            $reflection = new ReflectionClass($manager);
            $method = $reflection->getMethod('getNetworkConfigFromDatabase');
            $method->setAccessible(true);
            $network_config = $method->invoke($manager, $server['main_ip']);
            $network_config['hostname'] = $server['label'];
            
            // Log operation
            error_log("Using database network config for session: " . json_encode([
                'server_main_ip' => $server['main_ip'],
                'calculated_config' => $network_config
            ]), 3, "auto.logs");
            
            $stmt->execute([
                $server_id,
                $server_type,
                $server['label'],
                $server['mac'],
                $server['main_ip'],
                $server['label'],
                $data['os_template'],
                json_encode($network_config)
            ]);
            
            $session_id = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'session_id' => $session_id,
                'message' => 'Reinstall session created'
            ]);
            exit;
        }

        // Handle get_pxe_status endpoint for server installation monitoring
        if (isset($_GET['f']) && $_GET['f'] === 'get_pxe_status') {
            // Get statistics from pxe_reinstall_sessions
            $stats_stmt = $pdo->prepare("
                SELECT 
                    status,
                    COUNT(*) as count
                FROM pxe_reinstall_sessions 
                WHERE started_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY status
            ");
            $stats_stmt->execute();
            $status_counts = $stats_stmt->fetchAll(PDO::FETCH_ASSOC);

            // Initialize stats
            $stats = [
                'pending' => 0,
                'active' => 0, 
                'completed' => 0,
                'failed' => 0,
                'cancelled' => 0
            ];

            // Populate actual counts
            foreach ($status_counts as $row) {
                $stats[$row['status']] = (int)$row['count'];
            }

            // Get recent sessions (last 20)
            $recent_stmt = $pdo->prepare("
                SELECT 
                    s.id,
                    s.server_id,
                    s.server_type,
                    s.server_label,
                    s.mac_address,
                    s.ip_address,
                    s.hostname,
                    s.os_template,
                    s.status,
                    s.dhcp_configured,
                    s.files_created,
                    s.started_at,
                    s.completed_at,
                    s.error_message,
                    d.dhcp_entry_added,
                    d.dhcp_entry_removed,
                    d.is_active as dhcp_active
                FROM pxe_reinstall_sessions s
                LEFT JOIN pxe_dhcp_entries d ON s.id = d.session_id
                ORDER BY s.started_at DESC 
                LIMIT 20
            ");
            $recent_stmt->execute();
            $recent_sessions = $recent_stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate overall health status
            $total_active = $stats['pending'] + $stats['active'];
            $health_status = 'idle';
            
            if ($stats['failed'] > 0) {
                $health_status = 'error';
            } elseif ($total_active > 3) {
                $health_status = 'busy';
            } elseif ($total_active > 0) {
                $health_status = 'active';
            }

            // Format response
            $response = [
                'success' => true,
                'installation_health' => $health_status,
                'installation_stats' => $stats,
                'recent_installations' => $recent_sessions,
                'last_updated' => date('Y-m-d H:i:s')
            ];

            echo json_encode($response);
            exit;
        }

        // Handle cancel_pxe_session endpoint
        if (isset($_GET['f']) && $_GET['f'] === 'cancel_pxe_session') {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data || !isset($data['session_id'])) {
                throw new Exception('Session ID is required');
            }
            
            $session_id = $data['session_id'];
            
            // Get session details first
            $stmt = $pdo->prepare("
                SELECT s.*, d.mac_address, d.ip_address, d.hostname, d.server_label
                FROM pxe_reinstall_sessions s
                LEFT JOIN pxe_dhcp_entries d ON s.id = d.session_id
                WHERE s.id = ? AND s.status IN ('pending', 'active')
            ");
            $stmt->execute([$session_id]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                throw new Exception('Session not found or cannot be cancelled');
            }
            
            // Get server details for IPMI operations
            $server_table = $session['server_type'] === 'blade' ? 'blade_server_inventory' : 'inventory_dedicated_servers';
            $server_stmt = $pdo->prepare("SELECT * FROM $server_table WHERE id = ?");
            $server_stmt->execute([$session['server_id']]);
            $server = $server_stmt->fetch(PDO::FETCH_ASSOC);
            
            $cleanup_messages = [];
            
            // Step 1: Clean up PXE configuration files
            try {
                $servername = $manager->extractServerName($session['hostname']);
                $cleanup_result = $manager->cleanupPXEConfig(
                    $session['server_id'],
                    $servername,
                    $session['hostname']
                );
                $cleanup_messages[] = 'PXE configuration files cleaned up';
            } catch (Exception $e) {
                error_log("PXE cleanup error during cancellation: " . $e->getMessage(), 3, "auto.logs");
                $cleanup_messages[] = 'Warning: PXE file cleanup failed - ' . $e->getMessage();
            }
            
            // Step 2: Reset server boot order and restart via IPMI (if server has IPMI configured)
            if ($server && !empty($server['ipmi']) && !empty($server['ipmi_root_pass'])) {
                try {
                    $ipmi_address = $server['ipmi'];
                    $ipmi_username = 'root';
                    $ipmi_password = $server['ipmi_root_pass'];
                    
                    // Create secure temporary password file
                    $temp_password_file = tempnam(sys_get_temp_dir(), 'cancel_pxe_');
                    if ($temp_password_file === false) {
                        throw new Exception('Unable to create temporary password file for IPMI');
                    }
                    file_put_contents($temp_password_file, $ipmi_password);
                    chmod($temp_password_file, 0600);

                    $ipmi_address_clean  = escapeshellarg($ipmi_address);
                    $ipmi_username_clean = escapeshellarg($ipmi_username);
                    $password_file_clean = escapeshellarg($temp_password_file);

                    // Set boot device back to hard disk (persistent)
                    $hdd_cmd = "timeout 30s ipmitool -I lanplus -H {$ipmi_address_clean} -U {$ipmi_username_clean} -f {$password_file_clean} chassis bootdev disk options=persistent 2>&1";
                    exec($hdd_cmd, $hdd_out, $hdd_ret);
                    
                    if ($hdd_ret !== 0) {
                        // Try alternative command for setting HDD boot
                        $alt_hdd_cmd = "timeout 30s ipmitool -I lanplus -H {$ipmi_address_clean} -U {$ipmi_username_clean} -f {$password_file_clean} chassis bootparam set bootflag force_disk 2>&1";
                        exec($alt_hdd_cmd, $alt_hdd_out, $alt_hdd_ret);
                        if ($alt_hdd_ret !== 0) {
                            error_log('IPMI HDD boot setup failed: ' . implode(' ', array_merge($hdd_out, $alt_hdd_out)), 3, 'auto.logs');
                            throw new Exception('Failed to set normal boot order via IPMI');
                        }
                    }
                    
                    // Power cycle the server to boot normally
                    $pwr_cmd = "timeout 45s ipmitool -I lanplus -H {$ipmi_address_clean} -U {$ipmi_username_clean} -f {$password_file_clean} power cycle 2>&1";
                    exec($pwr_cmd, $pwr_out, $pwr_ret);
                    
                    if ($pwr_ret !== 0) {
                        error_log('IPMI power cycle failed during cancellation: ' . implode(' ', $pwr_out), 3, 'auto.logs');
                        $cleanup_messages[] = 'Warning: Server restart failed - manual intervention may be required';
                    } else {
                        $cleanup_messages[] = 'Server restarted with normal boot order';
                        error_log('Server restarted with normal boot order after PXE cancellation', 3, 'auto.logs');
                    }
                    
                    // Clean up temporary password file
                    unlink($temp_password_file);
                    
                } catch (Exception $ipmi_err) {
                    error_log('IPMI operation failed during PXE cancellation: ' . $ipmi_err->getMessage(), 3, 'auto.logs');
                    $cleanup_messages[] = 'Warning: IPMI restart failed - ' . $ipmi_err->getMessage();
                    
                    // Clean up temp file if it exists
                    if (isset($temp_password_file) && file_exists($temp_password_file)) {
                        unlink($temp_password_file);
                    }
                }
            } else {
                $cleanup_messages[] = 'Warning: No IPMI credentials available - server restart skipped';
            }
            
            // Step 3: Update database - mark session as cancelled and DHCP entry as removed
            $stmt = $pdo->prepare("
                UPDATE pxe_reinstall_sessions 
                SET status = 'cancelled', completed_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$session_id]);
            
            // Mark DHCP entry as removed if exists
            $stmt = $pdo->prepare("
                UPDATE pxe_dhcp_entries 
                SET dhcp_entry_removed = NOW(), is_active = 0 
                WHERE session_id = ? AND is_active = 1
            ");
            $stmt->execute([$session_id]);
            
            error_log("PXE installation cancelled for session $session_id: " . implode(', ', $cleanup_messages), 3, "auto.logs");
            
            echo json_encode([
                'success' => true,
                'message' => 'Installation cancelled successfully',
                'cleanup_details' => $cleanup_messages
            ]);
            exit;
        }

        // Handle retry_pxe_session endpoint
        if (isset($_GET['f']) && $_GET['f'] === 'retry_pxe_session') {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data || !isset($data['session_id'])) {
                throw new Exception('Session ID is required');
            }
            
            $session_id = $data['session_id'];
            
            // Reset session status to pending for retry
            $stmt = $pdo->prepare("
                UPDATE pxe_reinstall_sessions 
                SET status = 'pending', completed_at = NULL, error_message = NULL, started_at = NOW()
                WHERE id = ? AND status = 'failed'
            ");
            $stmt->execute([$session_id]);
            
            if ($stmt->rowCount() === 0) {
                throw new Exception('Session not found or cannot be retried');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Session marked for retry'
            ]);
            exit;
        }

        // Handle check_installation_status endpoint - Check if server has installation in progress
        if (isset($_GET['f']) && $_GET['f'] === 'check_installation_status') {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data || !isset($data['server_id'])) {
                throw new Exception('Server ID is required');
            }
            
            $server_id = $data['server_id'];
            
            // Check for active installation sessions for this server
            $stmt = $pdo->prepare("
                SELECT
                    s.id,
                    s.server_id,
                    s.server_type,
                    s.server_label,
                    s.os_template,
                    s.status,
                    s.started_at,
                    s.error_message,
                    s.acl_removed,
                    s.acl_reapplied,
                    s.acl_removal_error,
                    s.acl_reapply_error,
                    ro.name as os_name,
                    ro.version as os_version
                FROM pxe_reinstall_sessions s
                LEFT JOIN reinstallable_os ro ON CONCAT(ro.name, ' ', ro.version) COLLATE utf8mb4_general_ci = s.os_template COLLATE utf8mb4_general_ci
                WHERE s.server_id = ?
                  AND s.status IN ('pending', 'active')
                ORDER BY s.started_at DESC
                LIMIT 1
            ");
            $stmt->execute([$server_id]);
            $active_session = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($active_session) {
                // Installation is in progress
                $os_display_name = $active_session['os_name']
                    ? $active_session['os_name'] . ' ' . $active_session['os_version']
                    : $active_session['os_template'];

                echo json_encode([
                    'installation_in_progress' => true,
                    'session_id' => $active_session['id'],
                    'os_name' => $os_display_name,
                    'status' => $active_session['status'],
                    'started_at' => $active_session['started_at'],
                    'server_label' => $active_session['server_label'],
                    'error_message' => $active_session['error_message'],
                    'acl_status' => [
                        'removed' => (bool)$active_session['acl_removed'],
                        'reapplied' => (bool)$active_session['acl_reapplied'],
                        'removal_error' => $active_session['acl_removal_error'],
                        'reapply_error' => $active_session['acl_reapply_error']
                    ]
                ]);
            } else {
                // No installation in progress
                echo json_encode([
                    'installation_in_progress' => false
                ]);
            }
            exit;
        }

        // Handle reserve_installation endpoint - Reserve installation slot to prevent race conditions
        if (isset($_GET['f']) && $_GET['f'] === 'reserve_installation') {
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!$data || !isset($data['server_id'])) {
                throw new Exception('Server ID is required');
            }
            
            $server_id = $data['server_id'];
            $os_name = $data['os_name'] ?? 'Unknown OS';
            
            // Use database transaction to prevent race conditions
            $pdo->beginTransaction();
            
            try {
                // Check again for active installations (within transaction)
                $check_stmt = $pdo->prepare("
                    SELECT id, server_label, os_template, status, started_at
                    FROM pxe_reinstall_sessions 
                    WHERE server_id = ? 
                      AND status IN ('pending', 'active')
                    ORDER BY started_at DESC 
                    LIMIT 1
                    FOR UPDATE
                ");
                $check_stmt->execute([$server_id]);
                $existing_session = $check_stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($existing_session) {
                    $pdo->rollBack();
                    
                    echo json_encode([
                        'success' => false,
                        'error' => 'Another installation is already in progress for this server',
                        'existing_session' => [
                            'id' => $existing_session['id'],
                            'os_template' => $existing_session['os_template'],
                            'status' => $existing_session['status'],
                            'started_at' => $existing_session['started_at']
                        ]
                    ]);
                    exit;
                }
                
                // No existing installation, reservation successful
                $pdo->commit();
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Installation slot reserved successfully',
                    'server_id' => $server_id,
                    'reserved_for_os' => $os_name,
                    'reserved_at' => date('Y-m-d H:i:s')
                ]);
                
            } catch (Exception $e) {
                $pdo->rollBack();
                throw new Exception('Failed to reserve installation slot: ' . $e->getMessage());
            }
            exit;
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        // BusyBox wget on ESXi crashes on POST; some installers therefore send the
        // parameters via query string.  If the JSON body is empty, fall back to the
        // combined request array so that ?action=cleanup&server_id=... still works.
        if (!$data || !is_array($data) || empty($data)) {
            $data = $_REQUEST;
        }

        if (!$data || !is_array($data) || empty($data)) {
            throw new Exception('Invalid data payload');
        }
        
        switch ($action) {
            case 'setup_dhcp':
                $result = $manager->createDHCPReservation(
                    $data['server_id'],
                    $data['mac_address'],
                    $data['network_config'],
                    $data['dhcp_config'] ?? []
                );
                break;
                
            case 'setup_pxe':
                $result = $manager->setupPXEServer(
                    $data['server_id'],
                    $data['mac_address'],
                    $data['network_config'],
                    $data['os_template']
                );
                break;
                
            case 'setup_complete_reinstall':
                $result = $manager->setupCompleteReinstall(
                    $data['server_id'],
                    $data['server_data'],
                    $data['network_config'],
                    $data['os_template'],
                    $data['custom_password'] ?? null,
                    $data['session_id'] ?? null
                );
                break;
                
            case 'execute_server_reinstall':
                // New action for server reinstall from frontend
                if (!isset($data['server_id']) || !isset($data['server_type']) || !isset($data['os_template'])) {
                    throw new Exception('Server ID, type, and OS template are required');
                }
                
                $server_id = $data['server_id'];
                $server_type = $data['server_type'];
                $os_template = $data['os_template'];
                $custom_password = $data['custom_password'] ?? null;
                
                // Critical: Check for existing installations before proceeding
                $existing_check = $pdo->prepare("
                    SELECT id, server_label, os_template, status, started_at
                    FROM pxe_reinstall_sessions 
                    WHERE server_id = ? 
                      AND status IN ('pending', 'active')
                    ORDER BY started_at DESC 
                    LIMIT 1
                ");
                $existing_check->execute([$server_id]);
                $existing_session = $existing_check->fetch(PDO::FETCH_ASSOC);
                
                if ($existing_session) {
                    throw new Exception(sprintf(
                        'Another installation is already in progress for this server (Session ID: %d, OS: %s, Status: %s, Started: %s)',
                        $existing_session['id'],
                        $existing_session['os_template'],
                        $existing_session['status'],
                        $existing_session['started_at']
                    ));
                }
                
                // Get server data from database
                if ($server_type === 'blade') {
                    $stmt = $pdo->prepare("SELECT * FROM blade_server_inventory WHERE id = ?");
                } else {
                    $stmt = $pdo->prepare("SELECT * FROM inventory_dedicated_servers WHERE id = ?");
                }
                
                $stmt->execute([$server_id]);
                $server = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$server) {
                    throw new Exception('Server not found');
                }
                
                // Validate required fields
                if (!$server['mac']) {
                    throw new Exception('Server MAC address is required for PXE reinstall');
                }
                
                if (!$server['main_ip']) {
                    throw new Exception('Server main IP address is required for PXE reinstall');
                }
                
                // Always generate a new password for each reinstall unless explicitly provided
                if (!$custom_password) {
                    // Use the manager instance to generate the password
                    $custom_password = $manager->generateRandomPassword(16);
                    error_log("Generated new password for server reinstall ID: $server_id, length: " . strlen($custom_password), 3, "auto.logs");
                }
                
                // Use reflection to access the private getNetworkConfigFromDatabase method
                $reflection = new ReflectionClass($manager);
                $method = $reflection->getMethod('getNetworkConfigFromDatabase');
                $method->setAccessible(true);
                $network_config = $method->invoke($manager, $server['main_ip']);
                $network_config['hostname'] = $server['label'];
                
                // Log operation
                error_log("Using database network config instead of provided config: " . json_encode([
                    'server_main_ip' => $server['main_ip'],
                    'calculated_config' => $network_config
                ]), 3, "auto.logs");
                
                // Create server data structure
                $server_data = [
                    'mac' => $server['mac'],
                    'label' => $server['label'],
                    'main_ip' => $server['main_ip'],
                    'additional_ips' => $server['additional_ips'] ?? ''
                ];
                
                // Create reinstall session in database
                $stmt = $pdo->prepare("
                    INSERT INTO pxe_reinstall_sessions 
                    (server_id, server_type, server_label, mac_address, ip_address, hostname, os_template, network_config, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')
                ");
                
                $stmt->execute([
                    $server_id,
                    $server_type,
                    $server['label'],
                    $server['mac'],
                    $server['main_ip'],
                    $server['label'],
                    $os_template,
                    json_encode($network_config)
                ]);
                
                $session_id = $pdo->lastInsertId();

                try {
                    // Step 1: Remove existing ACLs before starting PXE reinstall
                    error_log("=== PXE REINSTALL: Starting ACL removal for server $server_id ($server_type) ===", 3, "auto.logs");
                    $acl_removal_result = $manager->removeServerACLs($server_id, $server_type);
                    error_log("ACL removal result: " . json_encode($acl_removal_result), 3, "auto.logs");

                    // Update session with ACL removal status
                    $acl_removed = $acl_removal_result['acl_removed'] ? 1 : 0;
                    $acl_error = isset($acl_removal_result['error']) ? $acl_removal_result['error'] : null;

                    $acl_update_stmt = $pdo->prepare("
                        UPDATE pxe_reinstall_sessions
                        SET acl_removed = ?, acl_removal_error = ?
                        WHERE id = ?
                    ");
                    $acl_update_stmt->execute([$acl_removed, $acl_error, $session_id]);

                    if ($acl_removal_result['acl_removed']) {
                        error_log("ACL removal successful for server $server_id: " . $acl_removal_result['message'], 3, "auto.logs");
                    } else {
                        error_log("ACL removal skipped or failed for server $server_id: " . $acl_removal_result['message'], 3, "auto.logs");
                        if (isset($acl_removal_result['warning'])) {
                            error_log("ACL removal warning: " . $acl_removal_result['warning'], 3, "auto.logs");
                        }
                        if (isset($acl_removal_result['error'])) {
                            error_log("ACL removal error: " . $acl_removal_result['error'], 3, "auto.logs");
                        }
                    }

                    // Step 2: Execute the complete reinstall
                    $result = $manager->setupCompleteReinstall(
                        $server_id,
                        $server_data,
                        $network_config,
                        $os_template,
                        $custom_password,
                        $session_id
                    );

                    /* ---------- IPMI: set PXE boot and power cycle ---------- */
                    try {
                        // Determine IPMI credentials
                        $ipmi_address   = $data['ipmi_address']   ?? ($server['ipmi'] ?? null);
                        $ipmi_username  = $data['ipmi_username']  ?? 'root';
                        $ipmi_password  = $data['ipmi_password']  ?? ($server['ipmi_root_pass'] ?? null);

                        if (!$ipmi_address || !$ipmi_password) {
                            throw new Exception('Missing IPMI credentials, skipping IPMI reboot');
                        }

                        // Create secure temporary password file
                        $temp_password_file = tempnam(sys_get_temp_dir(), 'pxe_');
                        if ($temp_password_file === false) {
                            throw new Exception('Unable to create temporary password file');
                        }
                        file_put_contents($temp_password_file, $ipmi_password);
                        chmod($temp_password_file, 0600);

                        $ipmi_address_clean  = escapeshellarg($ipmi_address);
                        $ipmi_username_clean = escapeshellarg($ipmi_username);
                        $password_file_clean = escapeshellarg($temp_password_file);

                        // Set bootdev to PXE (persistent)
                        $pxe_cmd = "timeout 30s ipmitool -I lanplus -H {$ipmi_address_clean} -U {$ipmi_username_clean} -f {$password_file_clean} chassis bootdev pxe options=persistent 2>&1";
                        exec($pxe_cmd, $pxe_out, $pxe_ret);
                        if ($pxe_ret !== 0) {
                            // fall back to alternative command
                            $alt_cmd = "timeout 30s ipmitool -I lanplus -H {$ipmi_address_clean} -U {$ipmi_username_clean} -f {$password_file_clean} chassis bootparam set bootflag force_pxe 2>&1";
                            exec($alt_cmd, $alt_out, $alt_ret);
                            if ($alt_ret !== 0) {
                                error_log('IPMI PXE boot setup failed: ' . implode(' ', array_merge($pxe_out, $alt_out)), 3, 'auto.logs');
                                throw new Exception('IPMI PXE boot setup failed');
                            }
                        }
                        // Power cycle
                        $pwr_cmd = "timeout 45s ipmitool -I lanplus -H {$ipmi_address_clean} -U {$ipmi_username_clean} -f {$password_file_clean} power cycle 2>&1";
                        exec($pwr_cmd, $pwr_out, $pwr_ret);
                        if ($pwr_ret !== 0) {
                            error_log('IPMI power cycle failed: ' . implode(' ', $pwr_out), 3, 'auto.logs');
                            throw new Exception('IPMI power cycle failed');
                        }
                        error_log('IPMI PXE boot configured and server power-cycled', 3, 'auto.logs');
                    } catch (Exception $ipmi_err) {
                        error_log('IPMI operation skipped/error: ' . $ipmi_err->getMessage(), 3, 'auto.logs');
                        // continue without failing reinstall
                    } finally {
                        if (isset($temp_password_file) && file_exists($temp_password_file)) {
                            unlink($temp_password_file);
                        }
                    }

                    /* ---------- END IPMI ---------- */

                    // Update session with success
                    $stmt = $pdo->prepare("
                        UPDATE pxe_reinstall_sessions 
                        SET status = 'active', dhcp_configured = 1, files_created = 1 
                        WHERE id = ?
                    ");
                    $stmt->execute([$session_id]);
                    
                    // Add to tracking table
                    $stmt = $pdo->prepare("
                        INSERT INTO pxe_dhcp_entries 
                        (session_id, mac_address, ip_address, hostname, server_label) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $session_id,
                        $server['mac'],
                        $server['main_ip'],
                        $server['label'],
                        $server['label']
                    ]);
                    
                    $result['session_id'] = $session_id;
                    
                } catch (Exception $e) {
                    // Update session with error
                    $stmt = $pdo->prepare("
                        UPDATE pxe_reinstall_sessions 
                        SET status = 'failed', error_message = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$e->getMessage(), $session_id]);
                    throw $e;
                }
                break;
                
            case 'cleanup':
                $servername = $data['servername'] ?? $manager->extractServerName($data['hostname'] ?? '');
                $result = $manager->cleanupPXEConfig(
                    $data['server_id'],
                    $servername,
                    $data['hostname']
                );
                
                // Update session status if session_id provided
                if (isset($data['session_id'])) {
                    // Get session details for ACL reapplication
                    $session_stmt = $pdo->prepare("
                        SELECT server_id, server_type
                        FROM pxe_reinstall_sessions
                        WHERE id = ?
                    ");
                    $session_stmt->execute([$data['session_id']]);
                    $session_data = $session_stmt->fetch(PDO::FETCH_ASSOC);

                    // Update session status to completed
                    $stmt = $pdo->prepare("
                        UPDATE pxe_reinstall_sessions
                        SET status = 'completed', completed_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$data['session_id']]);

                    // Mark DHCP entry as removed
                    $stmt = $pdo->prepare("
                        UPDATE pxe_dhcp_entries
                        SET dhcp_entry_removed = NOW(), is_active = 0
                        WHERE session_id = ?
                    ");
                    $stmt->execute([$data['session_id']]);

                    // Reapply ACLs after successful PXE reinstall completion
                    if ($session_data) {
                        error_log("Starting ACL reapplication for server {$session_data['server_id']} after PXE reinstall completion", 3, "auto.logs");
                        $acl_reapply_result = $manager->reapplyServerACLs($session_data['server_id'], $session_data['server_type']);

                        // Update session with ACL reapplication status
                        $acl_reapplied = ($acl_reapply_result['success'] && $acl_reapply_result['acl_applied']) ? 1 : 0;
                        $acl_reapply_error = isset($acl_reapply_result['error']) ? $acl_reapply_result['error'] : null;

                        $acl_reapply_update_stmt = $pdo->prepare("
                            UPDATE pxe_reinstall_sessions
                            SET acl_reapplied = ?, acl_reapply_error = ?
                            WHERE id = ?
                        ");
                        $acl_reapply_update_stmt->execute([$acl_reapplied, $acl_reapply_error, $data['session_id']]);

                        if ($acl_reapply_result['success'] && $acl_reapply_result['acl_applied']) {
                            error_log("ACL reapplication successful for server {$session_data['server_id']}: " . $acl_reapply_result['message'], 3, "auto.logs");
                            $result['acl_reapplication'] = [
                                'success' => true,
                                'message' => $acl_reapply_result['message'],
                                'subnets_configured' => $acl_reapply_result['subnets_configured'] ?? 0
                            ];
                        } else {
                            error_log("ACL reapplication failed or skipped for server {$session_data['server_id']}: " . $acl_reapply_result['message'], 3, "auto.logs");
                            if (isset($acl_reapply_result['error'])) {
                                error_log("ACL reapplication error: " . $acl_reapply_result['error'], 3, "auto.logs");
                            }
                            if (isset($acl_reapply_result['warning'])) {
                                error_log("ACL reapplication warning: " . $acl_reapply_result['warning'], 3, "auto.logs");
                            }
                            $result['acl_reapplication'] = [
                                'success' => $acl_reapply_result['success'],
                                'message' => $acl_reapply_result['message'],
                                'error' => $acl_reapply_result['error'] ?? null,
                                'warning' => $acl_reapply_result['warning'] ?? null
                            ];
                        }
                    } else {
                        error_log("Could not retrieve session data for ACL reapplication", 3, "auto.logs");
                        $result['acl_reapplication'] = [
                            'success' => false,
                            'message' => 'Could not retrieve session data for ACL reapplication'
                        ];
                    }
                }
                break;
                
            default:
                throw new Exception('Invalid action specified');
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code(500);
        error_log("PXE API error: " . $e->getMessage(), 3, "auto.logs");
        error_log("Stack trace: " . $e->getTraceAsString(), 3, "auto.logs");
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}

// Handle Proxmox VE automated installer HTTP POST requests for answer files
// This endpoint responds to POST requests from Proxmox VE 8.x automated installer
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_GET['action'])) {
    // Set appropriate headers for TOML response
    header('Content-Type: text/plain; charset=utf-8');
    
    try {
        // Read JSON request body from Proxmox installer
        $json_input = file_get_contents('php://input');
        $request_data = json_decode($json_input, true);
        
        // Log the request for debugging
        error_log("Proxmox auto-installer request: " . $json_input, 3, "auto.logs");
        
        if (!$request_data) {
            throw new Exception('Invalid JSON request');
        }
        
        // Extract system information from Proxmox request
        $system_info = [
            'product' => $request_data['product'] ?? [],
            'dmi' => $request_data['dmi'] ?? [],
            'network_interfaces' => $request_data['network_interfaces'] ?? []
        ];
        
        // Try to identify the server based on MAC address or DMI information
        $mac_address = null;
        if (!empty($system_info['network_interfaces'])) {
            // Get the first network interface MAC address
            $mac_address = $system_info['network_interfaces'][0]['mac'] ?? null;
        }
        
        $dmi_serial = $system_info['dmi']['system']['serial'] ?? null;
        $dmi_uuid = $system_info['dmi']['system']['uuid'] ?? null;
        
        // Search for matching server in database
        $server_found = null;
        $network_config = null;
        $session_id = null;
        
        if ($mac_address) {
            // Try to find active PXE session by MAC address
            $stmt = $pdo->prepare("
                SELECT * FROM pxe_reinstall_sessions 
                WHERE mac_address = ? AND status = 'active' 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$mac_address]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($session) {
                $session_id = $session['id'];
                $network_config = json_decode($session['network_config'], true);
                $server_found = [
                    'label' => $session['server_label'],
                    'mac' => $session['mac_address'],
                    'main_ip' => $session['ip_address']
                ];
                
                error_log("Found active PXE session for MAC: $mac_address, Session ID: $session_id", 3, "auto.logs");
            }
        }
        
        // If no session found, try to match by MAC in server inventory
        if (!$server_found && $mac_address) {
            // Try blade servers first
            $stmt = $pdo->prepare("SELECT * FROM blade_server_inventory WHERE mac = ? LIMIT 1");
            $stmt->execute([$mac_address]);
            $server_found = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // If not found, try dedicated servers
            if (!$server_found) {
                $stmt = $pdo->prepare("SELECT * FROM inventory_dedicated_servers WHERE mac = ? LIMIT 1");
                $stmt->execute([$mac_address]);
                $server_found = $stmt->fetch(PDO::FETCH_ASSOC);
            }
            
            if ($server_found) {
                // Generate network config from database
                ensurePXESchema($pdo);
                $manager = new PXENetworkManager($pdo);
                
                // Use reflection to access private method
                $reflection = new ReflectionClass($manager);
                $method = $reflection->getMethod('getNetworkConfigFromDatabase');
                $method->setAccessible(true);
                $network_config = $method->invoke($manager, $server_found['main_ip']);
                $network_config['hostname'] = $server_found['label'];
                $network_config['mac'] = $server_found['mac'];
                
                error_log("Found server in inventory for MAC: $mac_address, Server: " . $server_found['label'], 3, "auto.logs");
            }
        }
        
        // If still no server found, return error
        if (!$server_found || !$network_config) {
            error_log("No server found for Proxmox auto-installer request. MAC: $mac_address, DMI Serial: $dmi_serial", 3, "auto.logs");
            http_response_code(404);
            echo "# No configuration found for this system\n";
            echo "# MAC Address: " . ($mac_address ?? 'not found') . "\n";
            echo "# DMI Serial: " . ($dmi_serial ?? 'not found') . "\n";
            exit;
        }
        
        // Generate and return TOML answer file
        ensurePXESchema($pdo);
        $manager = new PXENetworkManager($pdo);
        
        $cleanup_info = [
            'server_id' => $server_found['id'] ?? null,
            'session_id' => $session_id,
            'hostname' => $network_config['hostname'],
            'servername' => $server_found['label']
        ];
        
        $server_data = [
            'mac' => $server_found['mac'],
            'label' => $server_found['label'],
            'main_ip' => $server_found['main_ip'],
            'additional_ips' => $server_found['additional_ips'] ?? ''
        ];
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($manager);
        $method = $reflection->getMethod('generateProxmoxAnswerFile');
        $method->setAccessible(true);
        $answer_file_content = $method->invoke($manager, $network_config, null, $cleanup_info, $server_data);
        
        error_log("Generated Proxmox answer file for " . $server_found['label'] . " (MAC: $mac_address)", 3, "auto.logs");
        
        // Return TOML content as plain text
        echo $answer_file_content;
        
    } catch (Exception $e) {
        error_log("Proxmox answer file error: " . $e->getMessage(), 3, "auto.logs");
        http_response_code(500);
        echo "# Error generating answer file: " . $e->getMessage() . "\n";
    }
    exit;
}
?> 